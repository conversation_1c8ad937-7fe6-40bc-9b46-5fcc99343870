import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/feedback/feedback_service.dart';
import '../../models/feedback_model.dart';
import '../theme/noeji_theme.dart';

class FeedbackPage extends ConsumerStatefulWidget {
  /// Whether this feedback panel was triggered manually (from context menu)
  /// vs automatically (from the trigger provider)
  final bool isManualTrigger;
  
  const FeedbackPage({
    super.key,
    this.isManualTrigger = false,
  });

  @override
  ConsumerState<FeedbackPage> createState() => _FeedbackPageState();
}

class _FeedbackPageState extends ConsumerState<FeedbackPage> {
  final TextEditingController _feedbackController = TextEditingController();
  int _selectedRating = 0;

  @override
  void initState() {
    super.initState();

    // Listen to text field changes to rebuild button state
    _feedbackController.addListener(() {
      setState(() {});
    });
    
    // Track impression when feedback panel is shown
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final feedbackService = ref.read(feedbackServiceProvider);
        feedbackService.shouldShowFeedbackPanel(
          isManualTrigger: widget.isManualTrigger,
        );
      }
    });
  }

  @override
  void dispose() {
    _feedbackController.dispose();
    super.dispose();
  }

  void _dismissPage() {
    Navigator.of(context).pop();
  }

  bool get _hasUserInput {
    return _selectedRating > 0 || _feedbackController.text.trim().isNotEmpty;
  }

  void _submitFeedback() async {
    if (!_hasUserInput) return;

    final feedbackService = ref.read(feedbackServiceProvider);

    final feedback = await FeedbackModel.create(
      rating: _selectedRating,
      content: _feedbackController.text.trim(),
    );

    // Submit feedback (fire and forget)
    feedbackService.submitFeedback(feedback);

    // Check if widget is still mounted before using context
    if (!mounted) return;

    // Dismiss page immediately
    Navigator.of(context).pop();

    // Show notification after dismissing
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '🙏 Thank you for your feedback!',
          style: TextStyle(
            fontFamily: 'Afacad',
            color: Theme.of(context).brightness == Brightness.dark 
                ? Colors.black 
                : Colors.white,
          ),
        ),
        backgroundColor: Theme.of(context).brightness == Brightness.dark 
            ? Colors.white 
            : Colors.black,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  Widget _buildRatingFace(int rating) {
    final isSelected = _selectedRating == rating;
    final icons = ['😢', '😕', '😐', '😊', '😍'];

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedRating = rating;
        });
      },
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color:
              isSelected
                  ? (Theme.of(context).brightness == Brightness.dark
                      ? Colors.white.withValues(alpha: 0.2)
                      : Colors.black.withValues(alpha: 0.15))
                  : Colors.transparent,
          border: Border.all(
            color: NoejiTheme.colorsOf(context).border,
            width: 1,
          ),
        ),
        child: Center(
          child: Text(icons[rating - 1], style: const TextStyle(fontSize: 24)),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final feedbackService = ref.watch(feedbackServiceProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).brightness == Brightness.dark 
          ? Colors.black 
          : Colors.white,
      body: SafeArea(
        child: _buildFeedbackContent(context, feedbackService),
      ),
    );
  }


  Widget _buildFeedbackContent(
    BuildContext context,
    FeedbackService feedbackService,
  ) {
    return Column(
      children: [
        // Top bar with back button
        Container(
          padding: const EdgeInsets.only(left: 8, top: 8, bottom: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: NoejiTheme.colorsOf(context).textPrimary,
                ),
                onPressed: _dismissPage,
              ),
            ],
          ),
        ),
        
        // Hero image - full width
        if (feedbackService.heroImageUrl.isNotEmpty)
          SizedBox(
            width: double.infinity,
            child: Image.network(
              feedbackService.heroImageUrl,
              fit: BoxFit.fitWidth,
              errorBuilder:
                  (context, error, stackTrace) => Container(
                    height: 150,
                    color: NoejiTheme.colorsOf(context).searchBarBackground,
                    child: Icon(
                      Icons.image,
                      size: 48,
                      color: NoejiTheme.colorsOf(context).textSecondary,
                    ),
                  ),
            ),
          ),

        // Message content - not scrollable
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 24),

                // Message content
                if (feedbackService.message.isNotEmpty)
                  Expanded(
                    child: MarkdownBody(
                      data: feedbackService.message,
                      styleSheet: MarkdownStyleSheet(
                        p: NoejiTheme.textStylesOf(
                          context,
                        ).bodyMedium.copyWith(height: 1.5),
                        h1: NoejiTheme.textStylesOf(context).titleLarge,
                        h2: NoejiTheme.textStylesOf(context).titleMedium,
                        h3: NoejiTheme.textStylesOf(context).titleSmall,
                      ),
                    ),
                  ),

                const SizedBox(height: 32),
              ],
            ),
          ),
        ),

        // Bottom section with rating and feedback
        Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Rating faces
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: List.generate(
                  5,
                  (index) => _buildRatingFace(index + 1),
                ),
              ),

              const SizedBox(height: 16),

              // Feedback text field
              TextField(
                controller: _feedbackController,
                maxLines: 3,
                textInputAction: TextInputAction.done,
                style: NoejiTheme.textStylesOf(context).bodyMedium,
                decoration: InputDecoration(
                  hintText:
                      "Share your feedback or report bugs and I will fix them asap...",
                  hintStyle: NoejiTheme.textStylesOf(
                    context,
                  ).bodyMedium.copyWith(
                    color: NoejiTheme.colorsOf(context).textSecondary,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(0),
                    borderSide: BorderSide(
                      color: NoejiTheme.colorsOf(context).border,
                      width: 1,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(0),
                    borderSide: BorderSide(
                      color: NoejiTheme.colorsOf(context).border,
                      width: 1,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(0),
                    borderSide: BorderSide(
                      color: Theme.of(context).primaryColor,
                      width: 1,
                    ),
                  ),
                  filled: true,
                  fillColor: NoejiTheme.colorsOf(context).searchBarBackground,
                ),
              ),

              const SizedBox(height: 16),

              // Submit button
              Align(
                alignment: Alignment.centerRight,
                child: ElevatedButton(
                  onPressed: _hasUserInput ? _submitFeedback : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(0),
                      side: BorderSide(
                        color: NoejiTheme.colorsOf(context).border,
                        width: 1,
                      ),
                    ),
                    textStyle: NoejiTheme.textStylesOf(context).buttonText,
                  ),
                  child: const Text('Submit Feedback'),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
