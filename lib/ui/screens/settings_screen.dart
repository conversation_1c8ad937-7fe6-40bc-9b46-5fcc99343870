import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:noeji/ui/widgets/account_deletion_dialog.dart';
import 'package:noeji/services/auth/auth_providers.dart';
import 'package:noeji/services/chat/chat_rate_limit_service_provider.dart';
import 'package:noeji/services/feedback/feedback_service.dart';
import 'package:noeji/services/haptic/haptic_feedback_provider.dart';
import 'package:noeji/services/limits/app_limits_providers.dart';
import 'package:noeji/services/preferences/app_behavior_provider.dart';
import 'package:noeji/services/subscription/subscription_providers.dart';
import 'package:noeji/services/tour/tour_service.dart';
import 'package:noeji/ui/providers/auth_provider.dart';
import 'package:noeji/ui/providers/debug_user_state_provider.dart';
import 'package:noeji/ui/providers/passcode_provider.dart';
import 'package:noeji/ui/screens/customize_system_prompt_screen.dart';
import 'package:noeji/ui/screens/passcode_screen.dart';
import 'package:noeji/ui/screens/welcome_screen.dart';
import 'package:noeji/ui/screens/custom_paywall_screen.dart';
import 'package:noeji/ui/theme/theme_provider.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/ui/widgets/chat_style_selector.dart';
import 'package:noeji/ui/widgets/color_palette_selector.dart';
import 'package:noeji/ui/widgets/noeji_pro_logo.dart';
import 'package:noeji/utils/feature_flags.dart';
import 'package:noeji/utils/logger.dart';
import 'package:noeji/models/enums.dart';

/// Settings screen
class SettingsScreen extends ConsumerStatefulWidget {
  /// Constructor
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen> {
  // State for Advanced Settings section
  bool _isAdvancedSettingsExpanded = false;
  
  // App version info
  String _appVersion = '';

  @override
  void initState() {
    super.initState();
    _loadAppVersion();
  }

  /// Load app version from package info
  Future<void> _loadAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      if (mounted) {
        setState(() {
          _appVersion = packageInfo.version;
        });
      }
    } catch (e) {
      Logger.error('Error loading app version', e);
    }
  }

  /// Determine if pro settings should be enabled based on user state
  bool _shouldEnableProSettings(UserState? userState) {
    if (userState == null) return false;

    switch (userState) {
      case UserState.trialUser:
      case UserState.proUser:
        return true;
      case UserState.newUser:
      case UserState.freeUser:
      case UserState.unknown:
        return false;
    }
  }

  /// Show the passcode change screen
  void _showChangePasscodeScreen(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: false,
      enableDrag: false,
      builder:
          (bottomSheetContext) => SizedBox(
            height: MediaQuery.of(bottomSheetContext).size.height * 0.9,
            child: PasscodeScreen(
              isChangingPasscode: true,
              onSuccess: () {
                Navigator.of(
                  bottomSheetContext,
                ).pop(); // Close the passcode screen
              },
              onCancel: () {
                Navigator.of(
                  bottomSheetContext,
                ).pop(); // Close the passcode screen
              },
            ),
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Get the current user
    final user = ref.watch(currentUserProvider);

    // If user is null, show error
    if (user == null) {
      return Scaffold(
        appBar: AppBar(
          title: Text(
            'Settings',
            style: GoogleFonts.afacad(
              fontSize: 20,
              fontWeight: FontWeight.w500,
              color: NoejiTheme.colorsOf(context).textPrimary,
            ),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ),
        body: Center(
          child: Text(
            'User not signed in',
            style: NoejiTheme.textStylesOf(context).bodyLarge,
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Settings',
          style: GoogleFonts.afacad(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: NoejiTheme.colorsOf(context).textPrimary,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Upgrade to Noeji PRO button (only for free users) - moved to top
            Consumer(
              builder: (context, ref, child) {
                final authProcess = ref.watch(authProcessProvider);
                final userStateAsync = ref.watch(realtimeUserStateProvider);

                // If signing out, hide the upgrade button during sign out
                if (authProcess == AuthProcess.signingOut) {
                  return const SizedBox.shrink();
                }

                return userStateAsync.when(
                  data: (userState) {
                    // Show for new users and free users (trial ended, not subscribed)
                    // Hide for trial users and pro users
                    if (userState == UserState.newUser ||
                        userState == UserState.freeUser) {
                      return Column(
                        children: [
                          Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: Theme.of(context).scaffoldBackgroundColor,
                              border: Border.all(
                                color: NoejiTheme.colorsOf(context).border,
                                width: 1,
                              ),
                            ),
                            child: InkWell(
                              onTap: () async {
                                try {
                                  // Show the custom paywall screen
                                  final purchased = await Navigator.of(
                                    context,
                                  ).push<bool>(
                                    MaterialPageRoute(
                                      builder:
                                          (context) =>
                                              const CustomPaywallScreen(),
                                      fullscreenDialog: true,
                                    ),
                                  );
                                  Logger.debug(
                                    'Paywall result: ${purchased ?? false}',
                                  );
                                } catch (e) {
                                  Logger.error(
                                    'Error showing paywall from App Behavior section',
                                    e,
                                  );
                                }
                              },
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 16.0,
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    // Primary text: "Upgrade to Noeji PRO"
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          'Upgrade to ',
                                          style: GoogleFonts.afacad(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w500,
                                            color:
                                                NoejiTheme.colorsOf(
                                                  context,
                                                ).textPrimary,
                                          ),
                                        ),
                                        const NoejiProLogo(height: 20),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    // Secondary text
                                    Text(
                                      'Unlock unlimited access to all Pro features',
                                      style: GoogleFonts.afacad(
                                        fontSize: 14,
                                        color:
                                            NoejiTheme.colorsOf(
                                              context,
                                            ).textSecondary,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 40),
                        ],
                      );
                    } else {
                      // Trial users and pro users don't show the button
                      return const SizedBox.shrink();
                    }
                  },
                  loading:
                      () => const SizedBox.shrink(), // Don't show while loading
                  error:
                      (error, stack) =>
                          const SizedBox.shrink(), // Don't show on error
                );
              },
            ),

            // Theme section
            Text(
              'Theme',
              style: GoogleFonts.afacad(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: NoejiTheme.colorsOf(context).textPrimary,
              ),
            ),

            const SizedBox(height: 16),

            // Theme selection buttons - single three-way selector
            Consumer(
              builder: (context, ref, child) {
                final themeMode = ref.watch(themeModeProvider);

                return Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: NoejiTheme.colorsOf(context).border,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      // Light theme button
                      Expanded(
                        child: _buildThemeSegment(
                          context: context,
                          ref: ref,
                          icon: Icons.wb_sunny,
                          label: 'Light',
                          isSelected: themeMode == ThemeMode.light,
                          onTap: () {
                            ref
                                .read(themeModeProvider.notifier)
                                .setThemeMode(ThemeMode.light);
                          },
                          showRightBorder: true,
                        ),
                      ),

                      // Dark theme button
                      Expanded(
                        child: _buildThemeSegment(
                          context: context,
                          ref: ref,
                          icon: Icons.nightlight_round,
                          label: 'Dark',
                          isSelected: themeMode == ThemeMode.dark,
                          onTap: () {
                            ref
                                .read(themeModeProvider.notifier)
                                .setThemeMode(ThemeMode.dark);
                          },
                          showRightBorder: true,
                        ),
                      ),

                      // System theme button
                      Expanded(
                        child: _buildThemeSegment(
                          context: context,
                          ref: ref,
                          icon: Icons.settings,
                          label: 'System',
                          isSelected: themeMode == ThemeMode.system,
                          onTap: () {
                            ref
                                .read(themeModeProvider.notifier)
                                .setThemeMode(ThemeMode.system);
                          },
                          showRightBorder: false,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),

            const SizedBox(height: 40),

            // Change passcode button
            Consumer(
              builder: (context, ref, child) {
                final isPasscodeSet =
                    ref.watch(passcodeSetCachedProvider).isSet;

                // Only show the option if a passcode is set
                if (isPasscodeSet) {
                  return Column(
                    children: [
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Theme.of(context).scaffoldBackgroundColor,
                          border: Border.all(
                            color: NoejiTheme.colorsOf(context).border,
                            width: 1,
                          ),
                        ),
                        child: InkWell(
                          onTap: () {
                            _showChangePasscodeScreen(context);
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 16.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.password,
                                  color:
                                      NoejiTheme.colorsOf(context).textPrimary,
                                  size: 24,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  'Change Ideabook Passcode',
                                  style: GoogleFonts.afacad(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color:
                                        NoejiTheme.colorsOf(
                                          context,
                                        ).textPrimary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 40),
                    ],
                  );
                } else {
                  return const SizedBox.shrink();
                }
              },
            ),

            // App Behavior section
            Consumer(
              builder: (context, ref, child) {
                final authProcess = ref.watch(authProcessProvider);
                final userStateAsync = ref.watch(realtimeUserStateProvider);

                // If signing out, hide the settings during sign out
                if (authProcess == AuthProcess.signingOut) {
                  return const SizedBox.shrink();
                }

                return userStateAsync.when(
                  data: (userState) {
                    final shouldEnableProSettings = _shouldEnableProSettings(
                      userState,
                    );

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Section title - clickable for free users to trigger paywall
                        GestureDetector(
                          onTap:
                              !shouldEnableProSettings
                                  ? () async {
                                    try {
                                      // Show the custom paywall screen
                                      final purchased = await Navigator.of(
                                        context,
                                      ).push<bool>(
                                        MaterialPageRoute(
                                          builder:
                                              (context) =>
                                                  const CustomPaywallScreen(),
                                          fullscreenDialog: true,
                                        ),
                                      );
                                      Logger.debug(
                                        'Paywall result: ${purchased ?? false}',
                                      );
                                    } catch (e) {
                                      Logger.error(
                                        'Error showing paywall from App Behavior section',
                                        e,
                                      );
                                    }
                                  }
                                  : null,
                          child: Text(
                            'App Behavior',
                            style: GoogleFonts.afacad(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color:
                                  shouldEnableProSettings
                                      ? NoejiTheme.colorsOf(context).textPrimary
                                      : NoejiTheme.colorsOf(
                                        context,
                                      ).textPrimary.withValues(alpha: 0.5),
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Send voice chat on finish setting
                        Consumer(
                          builder: (context, ref, child) {
                            final appBehavior = ref.watch(appBehaviorProvider);

                            if (!appBehavior.isLoaded) {
                              // Show loading state
                              return Opacity(
                                opacity: shouldEnableProSettings ? 1.0 : 0.5,
                                child: GestureDetector(
                                  onTap:
                                      !shouldEnableProSettings
                                          ? () async {
                                            try {
                                              // Show the custom paywall screen
                                              final purchased = await Navigator.of(
                                                context,
                                              ).push<bool>(
                                                MaterialPageRoute(
                                                  builder:
                                                      (context) =>
                                                          const CustomPaywallScreen(),
                                                  fullscreenDialog: true,
                                                ),
                                              );
                                              Logger.debug(
                                                'Paywall result: ${purchased ?? false}',
                                              );
                                            } catch (e) {
                                              Logger.error(
                                                'Error showing paywall from disabled section',
                                                e,
                                              );
                                            }
                                          }
                                          : null,
                                  child: Container(
                                    width: double.infinity,
                                    decoration: BoxDecoration(
                                      color:
                                          Theme.of(
                                            context,
                                          ).scaffoldBackgroundColor,
                                      border: Border.all(
                                        color:
                                            NoejiTheme.colorsOf(context).border,
                                        width: 1,
                                      ),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 16.0,
                                        horizontal: 16.0,
                                      ),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  'Send voice chat on finish',
                                                  style: GoogleFonts.afacad(
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.w500,
                                                    color:
                                                        NoejiTheme.colorsOf(
                                                          context,
                                                        ).textPrimary,
                                                  ),
                                                ),
                                                const SizedBox(height: 4),
                                                Text(
                                                  'Loading...',
                                                  style: GoogleFonts.afacad(
                                                    fontSize: 12,
                                                    color:
                                                        NoejiTheme.colorsOf(
                                                          context,
                                                        ).textSecondary,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          const SizedBox(
                                            width: 20,
                                            height: 20,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            }

                            return Opacity(
                              opacity: shouldEnableProSettings ? 1.0 : 0.5,
                              child: GestureDetector(
                                onTap:
                                    !shouldEnableProSettings
                                        ? () async {
                                          try {
                                            // Show the custom paywall screen
                                            final purchased = await Navigator.of(
                                              context,
                                            ).push<bool>(
                                              MaterialPageRoute(
                                                builder:
                                                    (context) =>
                                                        const CustomPaywallScreen(),
                                                fullscreenDialog: true,
                                              ),
                                            );
                                            Logger.debug(
                                              'Paywall result: ${purchased ?? false}',
                                            );
                                          } catch (e) {
                                            Logger.error(
                                              'Error showing paywall from disabled section',
                                              e,
                                            );
                                          }
                                        }
                                        : null,
                                child: Container(
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                    color:
                                        Theme.of(
                                          context,
                                        ).scaffoldBackgroundColor,
                                    border: Border.all(
                                      color:
                                          NoejiTheme.colorsOf(context).border,
                                      width: 1,
                                    ),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 16.0,
                                      horizontal: 16.0,
                                    ),
                                    child: Column(
                                      children: [
                                        // Press-and-hold to record new idea toggle
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    'Press-and-hold to record new idea',
                                                    style: GoogleFonts.afacad(
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      color:
                                                          NoejiTheme.colorsOf(
                                                            context,
                                                          ).textPrimary,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 4),
                                                  Text(
                                                    'Press and hold ideabook rows to record new ideas, mic buttons are hidden',
                                                    style: GoogleFonts.afacad(
                                                      fontSize: 14,
                                                      color:
                                                          NoejiTheme.colorsOf(
                                                            context,
                                                          ).textSecondary,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Switch(
                                              value:
                                                  appBehavior
                                                      .longPressToRecordNewIdea,
                                              onChanged:
                                                  shouldEnableProSettings
                                                      ? (value) {
                                                        ref
                                                            .read(
                                                              appBehaviorProvider
                                                                  .notifier,
                                                            )
                                                            .setLongPressToRecordNewIdea(
                                                              value,
                                                            );
                                                      }
                                                      : null, // Disabled for free users
                                              activeColor:
                                                  NoejiTheme.colorsOf(
                                                    context,
                                                  ).textPrimary,
                                            ),
                                          ],
                                        ),

                                        const SizedBox(height: 16),

                                        // Send voice chat on finish option
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    'Send voice chat on finish',
                                                    style: GoogleFonts.afacad(
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      color:
                                                          NoejiTheme.colorsOf(
                                                            context,
                                                          ).textPrimary,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 4),
                                                  Text(
                                                    'Voice messages are sent automatically when recording finishes',
                                                    style: GoogleFonts.afacad(
                                                      fontSize: 14,
                                                      color:
                                                          NoejiTheme.colorsOf(
                                                            context,
                                                          ).textSecondary,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Switch(
                                              value:
                                                  appBehavior
                                                      .sendVoiceChatOnFinish,
                                              onChanged:
                                                  shouldEnableProSettings
                                                      ? (value) {
                                                        ref
                                                            .read(
                                                              appBehaviorProvider
                                                                  .notifier,
                                                            )
                                                            .setSendVoiceChatOnFinish(
                                                              value,
                                                            );
                                                      }
                                                      : null, // Disabled for free users
                                              activeColor:
                                                  NoejiTheme.colorsOf(
                                                    context,
                                                  ).textPrimary,
                                            ),
                                          ],
                                        ),

                                        const SizedBox(height: 16),

                                        // Copy chat message as Markdown option
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    'Copy chat message as Markdown',
                                                    style: GoogleFonts.afacad(
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      color:
                                                          NoejiTheme.colorsOf(
                                                            context,
                                                          ).textPrimary,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 4),
                                                  Text(
                                                    'Chat messages are copied with markdown formatting',
                                                    style: GoogleFonts.afacad(
                                                      fontSize: 14,
                                                      color:
                                                          NoejiTheme.colorsOf(
                                                            context,
                                                          ).textSecondary,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Switch(
                                              value: appBehavior.copyAsMarkdown,
                                              onChanged:
                                                  shouldEnableProSettings
                                                      ? (value) {
                                                        ref
                                                            .read(
                                                              appBehaviorProvider
                                                                  .notifier,
                                                            )
                                                            .setCopyAsMarkdown(
                                                              value,
                                                            );
                                                      }
                                                      : null, // Disabled for free users
                                              activeColor:
                                                  NoejiTheme.colorsOf(
                                                    context,
                                                  ).textPrimary,
                                            ),
                                          ],
                                        ),

                                        const SizedBox(height: 16),

                                        // Suggest prompts by AI option
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    'Suggest prompts by AI',
                                                    style: GoogleFonts.afacad(
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      color:
                                                          NoejiTheme.colorsOf(
                                                            context,
                                                          ).textPrimary,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 4),
                                                  Text(
                                                    'Suggested prompts are dynamically generated by AI based on ideabook content',
                                                    style: GoogleFonts.afacad(
                                                      fontSize: 14,
                                                      color:
                                                          NoejiTheme.colorsOf(
                                                            context,
                                                          ).textSecondary,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Switch(
                                              value:
                                                  appBehavior
                                                      .generativeSuggestedPrompts,
                                              onChanged:
                                                  shouldEnableProSettings
                                                      ? (value) {
                                                        ref
                                                            .read(
                                                              appBehaviorProvider
                                                                  .notifier,
                                                            )
                                                            .setGenerativeSuggestedPrompts(
                                                              value,
                                                            );
                                                      }
                                                      : null, // Disabled for free users
                                              activeColor:
                                                  NoejiTheme.colorsOf(
                                                    context,
                                                  ).textPrimary,
                                            ),
                                          ],
                                        ),

                                        const SizedBox(height: 16),

                                        // Auto lock ideabooks option
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    'Auto lock ideabooks',
                                                    style: GoogleFonts.afacad(
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      color:
                                                          NoejiTheme.colorsOf(
                                                            context,
                                                          ).textPrimary,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 4),
                                                  Text(
                                                    'New ideabooks are automatically locked after creation',
                                                    style: GoogleFonts.afacad(
                                                      fontSize: 14,
                                                      color:
                                                          NoejiTheme.colorsOf(
                                                            context,
                                                          ).textSecondary,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Switch(
                                              value:
                                                  appBehavior.autoLockIdeabooks,
                                              onChanged:
                                                  shouldEnableProSettings
                                                      ? (value) {
                                                        ref
                                                            .read(
                                                              appBehaviorProvider
                                                                  .notifier,
                                                            )
                                                            .setAutoLockIdeabooks(
                                                              value,
                                                            );
                                                      }
                                                      : null, // Disabled for free users
                                              activeColor:
                                                  NoejiTheme.colorsOf(
                                                    context,
                                                  ).textPrimary,
                                            ),
                                          ],
                                        ),

                                        const SizedBox(height: 16),

                                        // Haptic feedback option
                                        Consumer(
                                          builder: (context, ref, child) {
                                            final hapticEnabled = ref.watch(hapticFeedbackProvider);
                                            return Row(
                                              children: [
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment.start,
                                                    children: [
                                                      Text(
                                                        'Haptic feedback',
                                                        style: GoogleFonts.afacad(
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                          color:
                                                              NoejiTheme.colorsOf(
                                                                context,
                                                              ).textPrimary,
                                                        ),
                                                      ),
                                                      const SizedBox(height: 4),
                                                      Text(
                                                        'Vibration feedback for taps, recordings, and interactions',
                                                        style: GoogleFonts.afacad(
                                                          fontSize: 14,
                                                          color:
                                                              NoejiTheme.colorsOf(
                                                                context,
                                                              ).textSecondary,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                Switch(
                                                  value: hapticEnabled,
                                                  onChanged: (value) {
                                                    ref
                                                        .read(
                                                          hapticFeedbackProvider.notifier,
                                                        )
                                                        .setEnabled(value);
                                                  },
                                                  activeColor:
                                                      NoejiTheme.colorsOf(
                                                        context,
                                                      ).textPrimary,
                                                ),
                                              ],
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ],
                    );
                  },
                  loading:
                      () => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'App Behavior',
                            style: GoogleFonts.afacad(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: NoejiTheme.colorsOf(context).textPrimary,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Container(
                            width: double.infinity,
                            height: 310, // Increased height for five options
                            decoration: BoxDecoration(
                              color: Theme.of(context).scaffoldBackgroundColor,
                              border: Border.all(
                                color: NoejiTheme.colorsOf(context).border,
                                width: 1,
                              ),
                            ),
                            child: const Center(
                              child: CircularProgressIndicator(),
                            ),
                          ),
                        ],
                      ),
                  error:
                      (error, stack) => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'App Behavior',
                            style: GoogleFonts.afacad(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: NoejiTheme.colorsOf(context).textPrimary,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: Theme.of(context).scaffoldBackgroundColor,
                              border: Border.all(
                                color: NoejiTheme.colorsOf(context).border,
                                width: 1,
                              ),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                vertical: 16.0,
                                horizontal: 16.0,
                              ),
                              child: Column(
                                children: [
                                  // Press-and-hold to record new idea option
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Press-and-hold to record new idea',
                                              style: GoogleFonts.afacad(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w500,
                                                color:
                                                    NoejiTheme.colorsOf(
                                                      context,
                                                    ).textPrimary,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              'Use mic buttons to record new ideas in ideabook rows',
                                              style: GoogleFonts.afacad(
                                                fontSize: 14,
                                                color:
                                                    NoejiTheme.colorsOf(
                                                      context,
                                                    ).textSecondary,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Switch(
                                        value: false,
                                        onChanged: null, // Disabled when error
                                        activeColor:
                                            NoejiTheme.colorsOf(
                                              context,
                                            ).textPrimary,
                                      ),
                                    ],
                                  ),

                                  const SizedBox(height: 16),

                                  // Send voice chat on finish option
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Send voice chat on finish',
                                              style: GoogleFonts.afacad(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w500,
                                                color:
                                                    NoejiTheme.colorsOf(
                                                      context,
                                                    ).textPrimary,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              'Voice messages are added to text input for editing before sending',
                                              style: GoogleFonts.afacad(
                                                fontSize: 14,
                                                color:
                                                    NoejiTheme.colorsOf(
                                                      context,
                                                    ).textSecondary,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Switch(
                                        value: false,
                                        onChanged: null, // Disabled when error
                                        activeColor:
                                            NoejiTheme.colorsOf(
                                              context,
                                            ).textPrimary,
                                      ),
                                    ],
                                  ),

                                  const SizedBox(height: 16),

                                  // Copy chat message as Markdown option
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Copy chat message as Markdown',
                                              style: GoogleFonts.afacad(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w500,
                                                color:
                                                    NoejiTheme.colorsOf(
                                                      context,
                                                    ).textPrimary,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              'Chat messages are copied as plain text without formatting',
                                              style: GoogleFonts.afacad(
                                                fontSize: 14,
                                                color:
                                                    NoejiTheme.colorsOf(
                                                      context,
                                                    ).textSecondary,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Switch(
                                        value: false,
                                        onChanged: null, // Disabled when error
                                        activeColor:
                                            NoejiTheme.colorsOf(
                                              context,
                                            ).textPrimary,
                                      ),
                                    ],
                                  ),

                                  const SizedBox(height: 16),

                                  // Suggest prompts by AI option
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Suggest prompts by AI',
                                              style: GoogleFonts.afacad(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w500,
                                                color:
                                                    NoejiTheme.colorsOf(
                                                      context,
                                                    ).textPrimary,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              'Suggested prompts use a static list of pre-defined prompts',
                                              style: GoogleFonts.afacad(
                                                fontSize: 14,
                                                color:
                                                    NoejiTheme.colorsOf(
                                                      context,
                                                    ).textSecondary,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Switch(
                                        value: false,
                                        onChanged: null, // Disabled when error
                                        activeColor:
                                            NoejiTheme.colorsOf(
                                              context,
                                            ).textPrimary,
                                      ),
                                    ],
                                  ),

                                  const SizedBox(height: 16),

                                  // Auto lock ideabooks option
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Auto lock ideabooks',
                                              style: GoogleFonts.afacad(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w500,
                                                color:
                                                    NoejiTheme.colorsOf(
                                                      context,
                                                    ).textPrimary,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              'New ideabooks are created in unlocked state by default',
                                              style: GoogleFonts.afacad(
                                                fontSize: 14,
                                                color:
                                                    NoejiTheme.colorsOf(
                                                      context,
                                                    ).textSecondary,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Switch(
                                        value: false,
                                        onChanged: null, // Disabled when error
                                        activeColor:
                                            NoejiTheme.colorsOf(
                                              context,
                                            ).textPrimary,
                                      ),
                                    ],
                                  ),

                                  const SizedBox(height: 16),

                                  // Haptic feedback option (error state)
                                  Consumer(
                                    builder: (context, ref, child) {
                                      final hapticEnabled = ref.watch(hapticFeedbackProvider);
                                      return Row(
                                        children: [
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  'Haptic feedback',
                                                  style: GoogleFonts.afacad(
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.w500,
                                                    color:
                                                        NoejiTheme.colorsOf(
                                                          context,
                                                        ).textPrimary,
                                                  ),
                                                ),
                                                const SizedBox(height: 4),
                                                Text(
                                                  'Vibration feedback for taps, recordings, and interactions',
                                                  style: GoogleFonts.afacad(
                                                    fontSize: 14,
                                                    color:
                                                        NoejiTheme.colorsOf(
                                                          context,
                                                        ).textSecondary,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Switch(
                                            value: hapticEnabled,
                                            onChanged: (value) {
                                              ref
                                                  .read(
                                                    hapticFeedbackProvider.notifier,
                                                  )
                                                  .setEnabled(value);
                                            },
                                            activeColor:
                                                NoejiTheme.colorsOf(
                                                  context,
                                                ).textPrimary,
                                          ),
                                        ],
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                );
              },
            ),

            const SizedBox(height: 40),

            // Ideabook Color Palette section
            Consumer(
              builder: (context, ref, child) {
                final authProcess = ref.watch(authProcessProvider);
                final userStateAsync = ref.watch(realtimeUserStateProvider);

                // If signing out, hide the color palette settings during sign out
                if (authProcess == AuthProcess.signingOut) {
                  return const SizedBox.shrink();
                }

                return userStateAsync.when(
                  data: (userState) {
                    final shouldEnableProSettings = _shouldEnableProSettings(
                      userState,
                    );

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Section title - clickable for free users to trigger paywall
                        GestureDetector(
                          onTap:
                              !shouldEnableProSettings
                                  ? () async {
                                    try {
                                      // Show the custom paywall screen
                                      final purchased = await Navigator.of(
                                        context,
                                      ).push<bool>(
                                        MaterialPageRoute(
                                          builder:
                                              (context) =>
                                                  const CustomPaywallScreen(),
                                          fullscreenDialog: true,
                                        ),
                                      );
                                      Logger.debug(
                                        'Paywall result: ${purchased ?? false}',
                                      );
                                    } catch (e) {
                                      Logger.error(
                                        'Error showing paywall from Color Palette section',
                                        e,
                                      );
                                    }
                                  }
                                  : null,
                          child: Text(
                            'Ideabook Color Palette',
                            style: GoogleFonts.afacad(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color:
                                  shouldEnableProSettings
                                      ? NoejiTheme.colorsOf(context).textPrimary
                                      : NoejiTheme.colorsOf(
                                        context,
                                      ).textPrimary.withValues(alpha: 0.5),
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Color palette selector container
                        Opacity(
                          opacity: shouldEnableProSettings ? 1.0 : 0.5,
                          child: GestureDetector(
                            onTap:
                                !shouldEnableProSettings
                                    ? () async {
                                      try {
                                        // Show the custom paywall screen
                                        final purchased = await Navigator.of(
                                          context,
                                        ).push<bool>(
                                          MaterialPageRoute(
                                            builder:
                                                (context) =>
                                                    const CustomPaywallScreen(),
                                            fullscreenDialog: true,
                                          ),
                                        );
                                        Logger.debug(
                                          'Paywall result: ${purchased ?? false}',
                                        );
                                      } catch (e) {
                                        Logger.error(
                                          'Error showing paywall from disabled color palette section',
                                          e,
                                        );
                                      }
                                    }
                                    : null,
                            child: Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color:
                                    Theme.of(context).scaffoldBackgroundColor,
                                border: Border.all(
                                  color:
                                      shouldEnableProSettings
                                          ? NoejiTheme.colorsOf(context).border
                                          : NoejiTheme.colorsOf(
                                            context,
                                          ).border.withValues(alpha: 0.5),
                                  width: 1,
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: IgnorePointer(
                                  ignoring: !shouldEnableProSettings,
                                  child: const ColorPaletteSelector(),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                  loading:
                      () => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Ideabook Color Palette',
                            style: GoogleFonts.afacad(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: NoejiTheme.colorsOf(context).textPrimary,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Container(
                            width: double.infinity,
                            height: 100,
                            decoration: BoxDecoration(
                              color: Theme.of(context).scaffoldBackgroundColor,
                              border: Border.all(
                                color: NoejiTheme.colorsOf(context).border,
                                width: 1,
                              ),
                            ),
                            child: const Center(
                              child: CircularProgressIndicator(),
                            ),
                          ),
                        ],
                      ),
                  error:
                      (error, stack) => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Ideabook Color Palette',
                            style: GoogleFonts.afacad(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: NoejiTheme.colorsOf(context).textPrimary,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: Theme.of(context).scaffoldBackgroundColor,
                              border: Border.all(
                                color: NoejiTheme.colorsOf(context).border,
                                width: 1,
                              ),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.all(16.0),
                              child: ColorPaletteSelector(),
                            ),
                          ),
                        ],
                      ),
                );
              },
            ),

            const SizedBox(height: 40),

            // Customize AI Chat Style section
            Consumer(
              builder: (context, ref, child) {
                final authProcess = ref.watch(authProcessProvider);
                final userStateAsync = ref.watch(realtimeUserStateProvider);

                // If signing out, hide the chat style settings during sign out
                if (authProcess == AuthProcess.signingOut) {
                  return const SizedBox.shrink();
                }

                return userStateAsync.when(
                  data: (userState) {
                    final shouldEnableProSettings = _shouldEnableProSettings(
                      userState,
                    );

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Section title - clickable for free users to trigger paywall
                        GestureDetector(
                          onTap:
                              !shouldEnableProSettings
                                  ? () async {
                                    try {
                                      // Show the custom paywall screen
                                      final purchased = await Navigator.of(
                                        context,
                                      ).push<bool>(
                                        MaterialPageRoute(
                                          builder:
                                              (context) =>
                                                  const CustomPaywallScreen(),
                                          fullscreenDialog: true,
                                        ),
                                      );
                                      Logger.debug(
                                        'Paywall result: ${purchased ?? false}',
                                      );
                                    } catch (e) {
                                      Logger.error(
                                        'Error showing paywall from Chat Style section',
                                        e,
                                      );
                                    }
                                  }
                                  : null,
                          child: Text(
                            'AI Chat Style',
                            style: GoogleFonts.afacad(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color:
                                  shouldEnableProSettings
                                      ? NoejiTheme.colorsOf(context).textPrimary
                                      : NoejiTheme.colorsOf(
                                        context,
                                      ).textPrimary.withValues(alpha: 0.5),
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Chat style selector container
                        Opacity(
                          opacity: shouldEnableProSettings ? 1.0 : 0.5,
                          child: GestureDetector(
                            onTap:
                                !shouldEnableProSettings
                                    ? () async {
                                      try {
                                        // Show the custom paywall screen
                                        final purchased = await Navigator.of(
                                          context,
                                        ).push<bool>(
                                          MaterialPageRoute(
                                            builder:
                                                (context) =>
                                                    const CustomPaywallScreen(),
                                            fullscreenDialog: true,
                                          ),
                                        );
                                        Logger.debug(
                                          'Paywall result: ${purchased ?? false}',
                                        );
                                      } catch (e) {
                                        Logger.error(
                                          'Error showing paywall from disabled chat style section',
                                          e,
                                        );
                                      }
                                    }
                                    : null,
                            child: Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color:
                                    Theme.of(context).scaffoldBackgroundColor,
                                border: Border.all(
                                  color:
                                      shouldEnableProSettings
                                          ? NoejiTheme.colorsOf(context).border
                                          : NoejiTheme.colorsOf(
                                            context,
                                          ).border.withValues(alpha: 0.5),
                                  width: 1,
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  children: [
                                    IgnorePointer(
                                      ignoring: !shouldEnableProSettings,
                                      child: const ChatStyleSelector(),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                  loading:
                      () => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'AI Chat Style',
                            style: GoogleFonts.afacad(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: NoejiTheme.colorsOf(context).textPrimary,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Container(
                            width: double.infinity,
                            height: 200,
                            decoration: BoxDecoration(
                              color: Theme.of(context).scaffoldBackgroundColor,
                              border: Border.all(
                                color: NoejiTheme.colorsOf(context).border,
                                width: 1,
                              ),
                            ),
                            child: const Center(
                              child: CircularProgressIndicator(),
                            ),
                          ),
                        ],
                      ),
                  error:
                      (error, stackTrace) => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'AI Chat Style',
                            style: GoogleFonts.afacad(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: NoejiTheme.colorsOf(context).textPrimary,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Container(
                            width: double.infinity,
                            height: 100,
                            decoration: BoxDecoration(
                              color: Theme.of(context).scaffoldBackgroundColor,
                              border: Border.all(
                                color: NoejiTheme.colorsOf(context).border,
                                width: 1,
                              ),
                            ),
                            child: Center(
                              child: Text(
                                'Error loading chat style options',
                                style: GoogleFonts.afacad(
                                  color:
                                      NoejiTheme.colorsOf(
                                        context,
                                      ).textSecondary,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                );
              },
            ),

            const SizedBox(height: 40),

            // Customize System Prompt button
            Consumer(
              builder: (context, ref, child) {
                final authProcess = ref.watch(authProcessProvider);
                final userStateAsync = ref.watch(realtimeUserStateProvider);

                // If signing out, hide during sign out
                if (authProcess == AuthProcess.signingOut) {
                  return const SizedBox.shrink();
                }

                return userStateAsync.when(
                  data: (userState) {
                    final shouldEnableProSettings = _shouldEnableProSettings(
                      userState,
                    );

                    return Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Theme.of(context).scaffoldBackgroundColor,
                        border: Border.all(
                          color:
                              shouldEnableProSettings
                                  ? NoejiTheme.colorsOf(context).border
                                  : NoejiTheme.colorsOf(
                                    context,
                                  ).border.withValues(alpha: 0.5),
                          width: 1,
                        ),
                      ),
                      child: InkWell(
                        onTap:
                            shouldEnableProSettings
                                ? () {
                                  Navigator.of(context).push(
                                    MaterialPageRoute(
                                      builder:
                                          (context) =>
                                              const CustomizeSystemPromptScreen(),
                                    ),
                                  );
                                }
                                : () async {
                                  try {
                                    // Show the custom paywall screen
                                    final purchased = await Navigator.of(
                                      context,
                                    ).push<bool>(
                                      MaterialPageRoute(
                                        builder:
                                            (context) =>
                                                const CustomPaywallScreen(),
                                        fullscreenDialog: true,
                                      ),
                                    );
                                    Logger.debug(
                                      'Paywall result: ${purchased ?? false}',
                                    );
                                  } catch (e) {
                                    Logger.error(
                                      'Error showing paywall from Customize System Prompt button',
                                      e,
                                    );
                                  }
                                },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 16.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.edit_note,
                                color:
                                    shouldEnableProSettings
                                        ? NoejiTheme.colorsOf(
                                          context,
                                        ).textPrimary
                                        : NoejiTheme.colorsOf(
                                          context,
                                        ).textPrimary.withValues(alpha: 0.5),
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'Customize System Prompt',
                                style: GoogleFonts.afacad(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color:
                                      shouldEnableProSettings
                                          ? NoejiTheme.colorsOf(
                                            context,
                                          ).textPrimary
                                          : NoejiTheme.colorsOf(
                                            context,
                                          ).textPrimary.withValues(alpha: 0.5),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                  loading:
                      () => Container(
                        width: double.infinity,
                        height: 60,
                        decoration: BoxDecoration(
                          color: Theme.of(context).scaffoldBackgroundColor,
                          border: Border.all(
                            color: NoejiTheme.colorsOf(context).border,
                            width: 1,
                          ),
                        ),
                        child: const Center(child: CircularProgressIndicator()),
                      ),
                  error:
                      (error, stackTrace) => Container(
                        width: double.infinity,
                        height: 60,
                        decoration: BoxDecoration(
                          color: Theme.of(context).scaffoldBackgroundColor,
                          border: Border.all(
                            color: NoejiTheme.colorsOf(context).border,
                            width: 1,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            'Error loading system prompt options',
                            style: GoogleFonts.afacad(
                              color: NoejiTheme.colorsOf(context).textSecondary,
                            ),
                          ),
                        ),
                      ),
                );
              },
            ),

            const SizedBox(height: 40),

            // Debug settings (only shown in debug mode)
            if (FeatureFlags.showDebugOptions) ...[
              Text(
                'Debug Settings',
                style: GoogleFonts.afacad(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: NoejiTheme.colorsOf(context).textPrimary,
                ),
              ),

              const SizedBox(height: 16),

              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  border: Border.all(
                    color: NoejiTheme.colorsOf(context).border,
                    width: 1,
                  ),
                ),
                child: InkWell(
                  onTap: () {
                    // Store the context before the async gap
                    final scaffoldMessenger = ScaffoldMessenger.of(context);

                    // Reset all tours
                    TourService.resetAllTours().then((result) {
                      // Show a snackbar to confirm
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          content: Text(
                            result
                                ? 'Tour tooltip states cleared. Tooltips will appear again when their conditions are met.'
                                : 'Failed to clear tour tooltip states',
                          ),
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    });
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.restart_alt,
                          color: NoejiTheme.colorsOf(context).textPrimary,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Clear Tour States',
                          style: GoogleFonts.afacad(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: NoejiTheme.colorsOf(context).textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Rate limit debug status button
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  border: Border.all(
                    color: NoejiTheme.colorsOf(context).border,
                    width: 1,
                  ),
                ),
                child: InkWell(
                  onTap: () {
                    // Debug rate limit status
                    Logger.debug('===== DEBUGGING RATE LIMIT STATUS =====');
                    ref
                        .read(chatRateLimitServiceProvider)
                        .debugRateLimitStatus()
                        .then((_) {
                          // Show a snackbar to confirm if context is still valid
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  'Rate limit status logged to console',
                                ),
                                duration: Duration(seconds: 2),
                              ),
                            );
                          }
                        });
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.bug_report,
                          color: NoejiTheme.colorsOf(context).textPrimary,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Debug Rate Limit Status',
                          style: GoogleFonts.afacad(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: NoejiTheme.colorsOf(context).textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Clear rate limit logs button
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  border: Border.all(
                    color: NoejiTheme.colorsOf(context).border,
                    width: 1,
                  ),
                ),
                child: InkWell(
                  onTap: () {
                    // Clear rate limit logs
                    Logger.debug(
                      '===== CLEARING RATE LIMIT LOGS FOR TESTING =====',
                    );
                    ref
                        .read(chatRateLimitServiceProvider)
                        .clearMessageLog()
                        .then((success) {
                          Logger.debug('Rate limit logs cleared: $success');

                          // Show a snackbar to confirm if context is still valid
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  'Rate limit logs cleared for testing',
                                ),
                                duration: Duration(seconds: 2),
                              ),
                            );
                          }
                        });
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.clear_all,
                          color: NoejiTheme.colorsOf(context).textPrimary,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Clear Rate Limit Logs',
                          style: GoogleFonts.afacad(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: NoejiTheme.colorsOf(context).textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Debug user state override
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  border: Border.all(
                    color: NoejiTheme.colorsOf(context).border,
                    width: 1,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Consumer(
                    builder: (context, ref, child) {
                      final debugUserState = ref.watch(debugUserStateProvider);
                      final debugUserStateNotifier = ref.watch(
                        debugUserStateProvider.notifier,
                      );

                      // Get the current real user state for display
                      final currentUserStateAsync = ref.watch(
                        realtimeUserStateProvider,
                      );

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Force User State (Debug)',
                                      style: GoogleFonts.afacad(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                        color:
                                            NoejiTheme.colorsOf(
                                              context,
                                            ).textPrimary,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      debugUserStateNotifier.debugDescription,
                                      style: GoogleFonts.afacad(
                                        fontSize: 14,
                                        color:
                                            NoejiTheme.colorsOf(
                                              context,
                                            ).textSecondary,
                                      ),
                                    ),

                                    // Show current user state
                                    const SizedBox(height: 8),
                                    currentUserStateAsync.when(
                                      loading:
                                          () => Text(
                                            'Current State: Loading...',
                                            style: GoogleFonts.afacad(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                              color:
                                                  NoejiTheme.colorsOf(
                                                    context,
                                                  ).textSecondary,
                                            ),
                                          ),
                                      error:
                                          (error, _) => Text(
                                            'Current State: Error',
                                            style: GoogleFonts.afacad(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                              color: Colors.red,
                                            ),
                                          ),
                                      data: (currentUserState) {
                                        String stateDisplayName;
                                        switch (currentUserState) {
                                          case UserState.newUser:
                                            stateDisplayName = 'New User';
                                            break;
                                          case UserState.trialUser:
                                            stateDisplayName = 'Trial User';
                                            break;
                                          case UserState.proUser:
                                            stateDisplayName = 'Pro User';
                                            break;
                                          case UserState.freeUser:
                                            stateDisplayName = 'Free User';
                                            break;
                                          case UserState.unknown:
                                            stateDisplayName = 'Unknown';
                                            break;
                                        }

                                        return Text(
                                          'Current State: $stateDisplayName',
                                          style: GoogleFonts.afacad(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w500,
                                            color:
                                                debugUserState == null
                                                    ? NoejiTheme.colorsOf(
                                                      context,
                                                    ).textPrimary
                                                    : NoejiTheme.colorsOf(
                                                      context,
                                                    ).textSecondary,
                                          ),
                                        );
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 16),

                          // User state selection buttons
                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: [
                              // Clear override button
                              _buildUserStateChip(
                                context: context,
                                label: 'Real State',
                                isSelected: debugUserState == null,
                                onTap: () {
                                  ref
                                      .read(debugUserStateProvider.notifier)
                                      .clearOverride();
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'Debug override cleared - using real user state',
                                      ),
                                      duration: Duration(seconds: 2),
                                    ),
                                  );
                                },
                              ),

                              // New User button
                              _buildUserStateChip(
                                context: context,
                                label: 'New User',
                                isSelected: debugUserState == UserState.newUser,
                                onTap: () {
                                  ref
                                      .read(debugUserStateProvider.notifier)
                                      .setUserState(UserState.newUser);
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'Debug user state set to: New User',
                                      ),
                                      duration: Duration(seconds: 2),
                                    ),
                                  );
                                },
                              ),

                              // Trial User button
                              _buildUserStateChip(
                                context: context,
                                label: 'Trial User',
                                isSelected:
                                    debugUserState == UserState.trialUser,
                                onTap: () {
                                  ref
                                      .read(debugUserStateProvider.notifier)
                                      .setUserState(UserState.trialUser);
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'Debug user state set to: Trial User',
                                      ),
                                      duration: Duration(seconds: 2),
                                    ),
                                  );
                                },
                              ),

                              // Pro User button
                              _buildUserStateChip(
                                context: context,
                                label: 'Pro User',
                                isSelected: debugUserState == UserState.proUser,
                                onTap: () {
                                  ref
                                      .read(debugUserStateProvider.notifier)
                                      .setUserState(UserState.proUser);
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'Debug user state set to: Pro User',
                                      ),
                                      duration: Duration(seconds: 2),
                                    ),
                                  );
                                },
                              ),

                              // Free User button
                              _buildUserStateChip(
                                context: context,
                                label: 'Free User',
                                isSelected:
                                    debugUserState == UserState.freeUser,
                                onTap: () {
                                  ref
                                      .read(debugUserStateProvider.notifier)
                                      .setUserState(UserState.freeUser);
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'Debug user state set to: Free User',
                                      ),
                                      duration: Duration(seconds: 2),
                                    ),
                                  );
                                },
                              ),

                              // Unknown User button
                              _buildUserStateChip(
                                context: context,
                                label: 'Unknown',
                                isSelected: debugUserState == UserState.unknown,
                                onTap: () {
                                  ref
                                      .read(debugUserStateProvider.notifier)
                                      .setUserState(UserState.unknown);
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'Debug user state set to: Unknown',
                                      ),
                                      duration: Duration(seconds: 2),
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Debug app limits button
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  border: Border.all(
                    color: NoejiTheme.colorsOf(context).border,
                    width: 1,
                  ),
                ),
                child: InkWell(
                  onTap: () async {
                    // Debug app limits
                    Logger.debug(
                      '===== DEBUGGING APP LIMITS FROM REMOTE CONFIG =====',
                    );
                    try {
                      final appLimitsService = ref.read(
                        appLimitsServiceProvider,
                      );
                      final userTierAsync = ref.read(userTierProvider);
                      final userTier = userTierAsync.value;

                      Logger.debug('Current user tier: $userTier');
                      appLimitsService.logCurrentLimits(userTier: userTier);

                      // Show a snackbar to confirm if context is still valid
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('App limits logged to console'),
                            duration: Duration(seconds: 2),
                          ),
                        );
                      }
                    } catch (e) {
                      Logger.error('Failed to debug app limits', e);
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'Failed to debug app limits - check console',
                            ),
                            duration: Duration(seconds: 2),
                          ),
                        );
                      }
                    }
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.settings_applications,
                          color: NoejiTheme.colorsOf(context).textPrimary,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Debug App Limits',
                          style: GoogleFonts.afacad(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: NoejiTheme.colorsOf(context).textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Reset feedback state button
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  border: Border.all(
                    color: NoejiTheme.colorsOf(context).border,
                    width: 1,
                  ),
                ),
                child: InkWell(
                  onTap: () async {
                    // Reset feedback state
                    Logger.debug('===== RESETTING FEEDBACK STATE =====');
                    try {
                      await ref.read(feedbackServiceProvider).resetFeedbackState();
                      
                      // Show a snackbar to confirm if context is still valid
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'Feedback state reset - auto feedback will appear again',
                            ),
                            duration: Duration(seconds: 2),
                          ),
                        );
                      }
                    } catch (e) {
                      Logger.error('Failed to reset feedback state', e);
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'Failed to reset feedback state - check console',
                            ),
                            duration: Duration(seconds: 2),
                          ),
                        );
                      }
                    }
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.feedback_outlined,
                          color: NoejiTheme.colorsOf(context).textPrimary,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Reset Feedback State',
                          style: GoogleFonts.afacad(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: NoejiTheme.colorsOf(context).textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],

            // Add some space before the Sign Out button
            const SizedBox(height: 40),

            // Sign Out button
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                border: Border.all(
                  color: NoejiTheme.colorsOf(context).border,
                  width: 1,
                ),
              ),
              child: InkWell(
                onTap: () async {
                  // Show confirmation dialog
                  final bool? confirmSignOut = await showDialog<bool>(
                    context: context,
                    builder: (BuildContext dialogContext) {
                      return AlertDialog(
                        title: Text(
                          'Sign Out',
                          style:
                              NoejiTheme.textStylesOf(dialogContext).bodyLarge,
                        ),
                        content: Text(
                          'Are you sure you want to sign out?',
                          style:
                              NoejiTheme.textStylesOf(dialogContext).bodyMedium,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.zero,
                          side: BorderSide(
                            color: NoejiTheme.colorsOf(dialogContext).border,
                            width: 1,
                          ),
                        ),
                        actions: [
                          TextButton(
                            onPressed:
                                () => Navigator.of(dialogContext).pop(false),
                            child: Text(
                              'Cancel',
                              style:
                                  NoejiTheme.textStylesOf(
                                    dialogContext,
                                  ).buttonText,
                            ),
                          ),
                          TextButton(
                            onPressed:
                                () => Navigator.of(dialogContext).pop(true),
                            child: Text(
                              'Sign Out',
                              style:
                                  NoejiTheme.textStylesOf(
                                    dialogContext,
                                  ).buttonText,
                            ),
                          ),
                        ],
                      );
                    },
                  );

                  // If user confirmed, proceed with sign out
                  if (confirmSignOut == true) {
                    try {
                      final signOut = ref.read(signOutProvider);
                      await signOut();
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Signed out successfully',
                              style: GoogleFonts.afacad(),
                            ),
                          ),
                        );
                      }
                    } catch (e) {
                      Logger.error('Error signing out', e);
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Error signing out: ${e.toString()}',
                              style: GoogleFonts.afacad(),
                            ),
                            backgroundColor: NoejiTheme.colorsOf(context).error,
                          ),
                        );
                      }
                    }
                  }
                },
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.logout,
                        color: NoejiTheme.colorsOf(context).textPrimary,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Sign Out',
                        style: GoogleFonts.afacad(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: NoejiTheme.colorsOf(context).textPrimary,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            const SizedBox(height: 32),

            // Advanced Settings section
            _buildAdvancedSettingsSection(context),

            const SizedBox(height: 40),

            // App User ID display at the bottom - centered
            Center(
              child: GestureDetector(
                onTap: () async {
                  try {
                    final appUserIdAsync = ref.read(appUserIdProvider);
                    final appUserId = appUserIdAsync.value;

                    if (appUserId != null) {
                      await Clipboard.setData(ClipboardData(text: appUserId));
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'App User ID copied to clipboard',
                              style: GoogleFonts.afacad(),
                            ),
                          ),
                        );
                      }
                    }
                  } catch (e) {
                    Logger.error('Error copying app user ID', e);
                  }
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'App User ID: ${ref.watch(appUserIdProvider).value ?? 'Loading...'}',
                      style: GoogleFonts.afacad(
                        fontSize: 12,
                        color: NoejiTheme.colorsOf(context).textSecondary,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Icon(
                      Icons.copy,
                      size: 14,
                      color: NoejiTheme.colorsOf(context).textSecondary,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 8),

            // App Version display
            Center(
              child: Text(
                'Version: ${_appVersion.isNotEmpty ? _appVersion : 'Loading...'}',
                style: GoogleFonts.afacad(
                  fontSize: 12,
                  color: NoejiTheme.colorsOf(context).textSecondary,
                ),
              ),
            ),

            // Add some bottom padding
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  /// Build the Advanced Settings collapsible section
  Widget _buildAdvancedSettingsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Advanced Settings header with arrow
        Container(
          width: double.infinity,
          color: Theme.of(context).scaffoldBackgroundColor,
          child: InkWell(
            onTap: () {
              setState(() {
                _isAdvancedSettingsExpanded = !_isAdvancedSettingsExpanded;
              });
            },
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Dangerous Zone',
                    style: GoogleFonts.afacad(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: NoejiTheme.colorsOf(context).textPrimary,
                    ),
                  ),
                  const SizedBox(width: 8),
                  AnimatedRotation(
                    turns: _isAdvancedSettingsExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: NoejiTheme.colorsOf(context).textPrimary,
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // Collapsible content
        AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          height: _isAdvancedSettingsExpanded ? null : 0,
          child: AnimatedOpacity(
            duration: const Duration(milliseconds: 200),
            opacity: _isAdvancedSettingsExpanded ? 1.0 : 0.0,
            child:
                _isAdvancedSettingsExpanded
                    ? Column(
                      children: [
                        const SizedBox(height: 16),
                        // Delete Account button
                        Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Theme.of(context).scaffoldBackgroundColor,
                            border: Border.all(color: Colors.red, width: 1),
                          ),
                          child: InkWell(
                            onTap: () async {
                              // Show confirmation dialog
                              final bool confirmed =
                                  await AccountDeletionDialogs.showConfirmationDialog(
                                    context,
                                  );

                              // If user confirmed, proceed with account deletion
                              if (confirmed && context.mounted) {
                                await _processAccountDeletion(context);
                              }
                            },
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.delete_forever,
                                    color: Colors.red,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 12),
                                  Text(
                                    'Delete Your Account and Data',
                                    style: GoogleFonts.afacad(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.red,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    )
                    : const SizedBox.shrink(),
          ),
        ),
      ],
    );
  }

  /// Build a theme selection segment for the three-way selector
  Widget _buildThemeSegment({
    required BuildContext context,
    required WidgetRef ref,
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
    required bool showRightBorder,
  }) {
    // Determine background color based on selection and theme
    Color backgroundColor;
    if (isSelected) {
      // Use a stronger contrast background for selected state
      final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
      backgroundColor =
          isDarkTheme
              ? NoejiTheme.colorsOf(context).textPrimary.withValues(alpha: 0.25)
              : NoejiTheme.colorsOf(
                context,
              ).textPrimary.withValues(alpha: 0.15);
    } else {
      backgroundColor = Theme.of(context).scaffoldBackgroundColor;
    }

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        border: Border(
          right:
              showRightBorder
                  ? BorderSide(
                    color: NoejiTheme.colorsOf(context).border,
                    width: 1,
                  )
                  : BorderSide.none,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: NoejiTheme.colorsOf(context).textPrimary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: GoogleFonts.afacad(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: NoejiTheme.colorsOf(context).textPrimary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Process account deletion using the new two-stage account deletion service
  Future<void> _processAccountDeletion(BuildContext context) async {
    if (!context.mounted) return;

    final accountDeletionService = ref.read(accountDeletionServiceProvider);
    bool reauthSuccessful = false;

    try {
      // --- STAGE 1: Prepare and Re-authenticate ---
      reauthSuccessful = await accountDeletionService
          .prepareForDeletionAndReauthenticate((
            String providerNameFriendly,
          ) async {
            // This callback is invoked by the service to show the "re-auth info" dialog
            return await AccountDeletionDialogs.showReauthenticationInfoDialog(
              context,
              providerNameFriendly,
            );
          });

      if (!reauthSuccessful) {
        // User cancelled the re-auth info dialog, or re-auth failed internally
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Account deletion process cancelled during identity verification.',
              ),
            ),
          );
        }
        return;
      }
    } catch (e) {
      // Handle exceptions specifically from prepareForDeletionAndReauthenticate
      Logger.error("Error during re-authentication preparation stage: $e");
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Identity verification failed: ${e.toString().replaceFirst("Exception: ", "")}',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
      return; // Stop the process
    }

    // --- STAGE 2: Final Deletion Confirmation ---
    if (!context.mounted) return;

    final bool confirmedFinalDelete =
        await AccountDeletionDialogs.showFinalDeletionConfirmationDialog(
          context,
        );

    if (!confirmedFinalDelete) {
      if (context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Account deletion cancelled.')));
      }
      return;
    }

    // --- STAGE 3: Execute Deletion ---
    if (!context.mounted) return;
    AccountDeletionDialogs.showLoadingDialog(context);

    try {
      await accountDeletionService.executeConfirmedDeletion();

      if (context.mounted) {
        AccountDeletionDialogs.hideLoadingDialog(context);

        // Navigate to welcome screen
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const WelcomeScreen()),
          (Route<dynamic> route) => false,
        );

        // Show success message
        AccountDeletionDialogs.showSuccessMessage(context);
      }
    } catch (e) {
      Logger.error("Error during final account execution stage: $e");
      if (context.mounted) {
        AccountDeletionDialogs.hideLoadingDialog(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Account deletion failed: ${e.toString().replaceFirst("Exception: ", "")}',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Build a user state selection chip
  Widget _buildUserStateChip({
    required BuildContext context,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? NoejiTheme.colorsOf(
                    context,
                  ).textPrimary.withValues(alpha: 0.1)
                  : Colors.transparent,
          border: Border.all(
            color:
                isSelected
                    ? NoejiTheme.colorsOf(context).textPrimary
                    : NoejiTheme.colorsOf(context).border,
            width: 1,
          ),
        ),
        child: Text(
          label,
          style: GoogleFonts.afacad(
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
            color:
                isSelected
                    ? NoejiTheme.colorsOf(context).textPrimary
                    : NoejiTheme.colorsOf(context).textSecondary,
          ),
        ),
      ),
    );
  }
}
