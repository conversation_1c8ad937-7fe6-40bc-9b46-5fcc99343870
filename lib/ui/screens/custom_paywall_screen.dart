import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/ui/widgets/noeji_pro_logo.dart';
import 'package:noeji/ui/screens/terms_of_service_screen.dart';
import 'package:noeji/ui/screens/privacy_policy_screen.dart';
import 'package:noeji/services/subscription/subscription_providers.dart';
import 'package:noeji/utils/logger.dart';
import 'package:noeji/services/rating/rating_service_provider.dart';

/// Custom paywall screen for new users
/// This screen is non-dismissible and shows subscription options with custom UI
class CustomPaywallScreen extends ConsumerStatefulWidget {
  const CustomPaywallScreen({super.key});

  @override
  ConsumerState<CustomPaywallScreen> createState() =>
      _CustomPaywallScreenState();
}

class _CustomPaywallScreenState extends ConsumerState<CustomPaywallScreen> {
  Offerings? _offerings;
  Package? _selectedPackage;
  Map<String, IntroEligibility> _introEligibility = {};
  bool _isLoading = true;
  bool _isPurchasing = false;

  @override
  void initState() {
    super.initState();
    
    // Record paywall shown time for rating service (captures all paywall trigger paths)
    final ratingService = ref.read(inAppRatingServiceProvider);
    ratingService.recordPaywallShown();
    Logger.debug('CustomPaywallScreen: Recorded paywall shown time for rating service');
    
    _fetchOfferings();
  }

  Future<void> _fetchOfferings() async {
    try {
      Logger.debug('CustomPaywallScreen: Fetching offerings from RevenueCat');
      final offerings = await Purchases.getOfferings();

      if (offerings.current != null &&
          offerings.current!.availablePackages.isNotEmpty) {
        // Get all product identifiers from the available packages
        final productIdentifiers =
            offerings.current!.availablePackages
                .map((p) => p.storeProduct.identifier)
                .toList();

        // Check the trial/intro eligibility for these products
        Logger.debug(
          'CustomPaywallScreen: Checking intro eligibility for products: $productIdentifiers',
        );
        final introEligibility =
            await Purchases.checkTrialOrIntroductoryPriceEligibility(
              productIdentifiers,
            );

        setState(() {
          _offerings = offerings;
          // Default to the first available package
          _selectedPackage = offerings.current!.availablePackages.first;
          // Store the fetched eligibility
          _introEligibility = introEligibility;
          _isLoading = false;
        });
        Logger.debug(
          'CustomPaywallScreen: Successfully loaded ${offerings.current!.availablePackages.length} packages',
        );
        Logger.debug(
          'CustomPaywallScreen: Eligibility status: $_introEligibility',
        );
      } else {
        Logger.debug('CustomPaywallScreen: No offerings available');
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      Logger.error('CustomPaywallScreen: Error fetching offerings', e);
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Make the screen non-dismissible
    return PopScope(
      canPop: false,
      child: Scaffold(
        body: Stack(
          children: [
            _isLoading ? _buildLoadingScreen() : _buildPaywallContent(),
            // Dismiss button in top right corner
            Positioned(
              top: MediaQuery.of(context).padding.top + 16,
              right: 20,
              child: GestureDetector(
                onTap: _dismissPaywall,
                child: SizedBox(
                  width: 48,
                  height: 48,
                  child: Center(
                    child: Icon(
                      Icons.close,
                      color: NoejiTheme.colorsOf(context).textPrimary,
                      size: 20,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            'Loading subscription options...',
            style: NoejiTheme.textStylesOf(context).bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildPaywallContent() {
    if (_offerings == null || _offerings!.current == null) {
      return _buildErrorScreen();
    }

    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Top section with logo and benefits - takes up available space
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Noeji Logo
                  Center(child: NoejiProLogo(height: 50)),
                  const SizedBox(height: 32),

                  // Invitation text
                  Text(
                    'We invite you to upgrade to continue using Noeji.',
                    style: GoogleFonts.afacad(
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      color: NoejiTheme.colorsOf(context).textPrimary,
                    ),
                    textAlign: TextAlign.left,
                  ),
                  const SizedBox(height: 24),

                  // Benefits List
                  _buildBenefitsList(),
                ],
              ),
            ),

            // Bottom section - fixed at bottom
            Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Package Selector
                if (_offerings!.current!.availablePackages.isNotEmpty) ...[
                  _buildPackageSelector(),
                  const SizedBox(height: 12),
                ],

                // Call to Action Button
                _buildCallToActionButton(),
                const SizedBox(height: 12),

                // Auto-renew text
                _buildAutoRenewText(),
                const SizedBox(height: 12),

                // Footer with all links in one line
                _buildFooter(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorScreen() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: NoejiTheme.colorsOf(context).error,
            ),
            const SizedBox(height: 16),
            Text(
              'Unable to load subscription options',
              style: NoejiTheme.textStylesOf(context).titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Please check your internet connection and try again.',
              style: NoejiTheme.textStylesOf(context).bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            OutlinedButton(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                });
                _fetchOfferings();
              },
              child: Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBenefitsList() {
    final benefits = [
      {'primary': 'Unlimited access to all features', 'secondary': ''},
      {
        'primary': 'Full control over AI',
        'secondary': 'Adjust tone, verbosity, and even system prompt.',
      },
      {
        'primary': 'Customize app behavior and look',
        'secondary': 'More export formats, custom color themes and more.',
      },
      {
        'primary': 'Priority access to new features',
        'secondary': 'E.g. collaborative ideabook is coming soon.',
      },
      {'primary': 'No commitment, cancel anytime', 'secondary': ''},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children:
          benefits
              .map(
                (benefit) => _BenefitItem(
                  primaryText: benefit['primary']!,
                  secondaryText: benefit['secondary']!,
                ),
              )
              .toList(),
    );
  }

  Widget _buildPackageSelector() {
    final packages = _offerings!.current!.availablePackages;

    if (packages.length == 1) {
      // If only one package, just show it selected
      return _buildPackageOption(packages.first, true);
    }

    return Column(
      children:
          packages.map((package) {
            final isSelected = package == _selectedPackage;
            return Padding(
              padding: const EdgeInsets.only(bottom: 12.0),
              child: GestureDetector(
                onTap: () => setState(() => _selectedPackage = package),
                child: _buildPackageOption(package, isSelected),
              ),
            );
          }).toList(),
    );
  }

  Widget _buildPackageOption(Package package, bool isSelected) {
    final product = package.storeProduct;
    final isYearly = product.identifier.contains('year');

    // Get the monthly package for price comparison (if available)
    Package? monthlyPackage;
    if (_offerings?.current?.availablePackages != null) {
      try {
        monthlyPackage = _offerings!.current!.availablePackages.firstWhere(
          (pkg) => !pkg.storeProduct.identifier.contains('year'),
        );
      } catch (e) {
        // No monthly package found
        monthlyPackage = null;
      }
    }

    // Build price display with billing period on same line
    Widget priceDisplay;
    if (isYearly) {
      // Calculate monthly equivalent from yearly price
      double yearlyPrice = product.price;
      double monthlyEquivalent = yearlyPrice / 12;

      // Format the monthly equivalent price
      String monthlyEquivalentFormatted =
          '\$${monthlyEquivalent.toStringAsFixed(2)}';

      // Get monthly package price for cross-out if available
      String? monthlyPriceString;
      if (monthlyPackage != null) {
        monthlyPriceString = monthlyPackage.storeProduct.priceString;
      }

      if (monthlyPriceString != null) {
        // Show crossed-out monthly price alongside calculated monthly price
        priceDisplay = Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Crossed out monthly price
                Text(
                  monthlyPriceString,
                  style: GoogleFonts.afacad(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: NoejiTheme.colorsOf(context).textSecondary,
                    decoration: TextDecoration.lineThrough,
                    decorationColor: NoejiTheme.colorsOf(context).textSecondary,
                  ),
                ),
                const SizedBox(width: 4),
                // Calculated monthly price - green and bold
                Text(
                  monthlyEquivalentFormatted,
                  style: GoogleFonts.afacad(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.green,
                  ),
                ),
                Text(
                  ' / month',
                  style: GoogleFonts.afacad(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: NoejiTheme.colorsOf(context).textPrimary,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  'billed yearly',
                  style: GoogleFonts.afacad(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: NoejiTheme.colorsOf(context).textSecondary,
                  ),
                ),
              ],
            ),
          ],
        );
      } else {
        // If no monthly package, just show the monthly equivalent
        priceDisplay = Row(
          children: [
            // Calculated monthly price - green and bold
            Text(
              '$monthlyEquivalentFormatted / month',
              style: GoogleFonts.afacad(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.green,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              'billed yearly',
              style: GoogleFonts.afacad(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: NoejiTheme.colorsOf(context).textSecondary,
              ),
            ),
          ],
        );
      }
    } else {
      priceDisplay = Row(
        children: [
          Text(
            '${product.priceString} / month',
            style: GoogleFonts.afacad(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: NoejiTheme.colorsOf(context).textPrimary,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            'billed monthly',
            style: GoogleFonts.afacad(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: NoejiTheme.colorsOf(context).textSecondary,
            ),
          ),
        ],
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            isSelected
                ? NoejiTheme.colorsOf(
                  context,
                ).textPrimary.withValues(alpha: 0.05)
                : Colors.transparent,
        border: Border.all(
          color:
              isSelected
                  ? NoejiTheme.colorsOf(context).textPrimary
                  : NoejiTheme.colorsOf(context).border,
          width: isSelected ? 2.0 : 1.0,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isYearly ? 'Annual' : 'Monthly',
                  style: GoogleFonts.afacad(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: NoejiTheme.colorsOf(context).textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                priceDisplay,
              ],
            ),
          ),
          if (isSelected)
            Icon(
              Icons.check_circle,
              color: NoejiTheme.colorsOf(context).textPrimary,
              size: 24,
            ),
        ],
      ),
    );
  }

  // String _getPeriodName(dynamic period) {
  //   // Handle both string and enum cases
  //   final periodStr = period.toString().toLowerCase();

  //   if (periodStr.contains('day')) {
  //     return 'day';
  //   } else if (periodStr.contains('week')) {
  //     return 'week';
  //   } else if (periodStr.contains('month')) {
  //     return 'month';
  //   } else if (periodStr.contains('year')) {
  //     return 'year';
  //   } else {
  //     // Fallback - try to extract meaningful text
  //     return periodStr.replaceAll(RegExp(r'[^a-z]'), '');
  //   }
  // }

  Widget _buildCallToActionButton() {
    if (_selectedPackage == null) return const SizedBox.shrink();

    final product = _selectedPackage!.storeProduct;

    // Check if the product itself has an intro price (trial)
    final hasIntroOffer = product.introductoryPrice != null;

    // Check if the USER is eligible for the intro offer
    final eligibility = _introEligibility[product.identifier];
    final isEligibleForTrial =
        eligibility?.status ==
        IntroEligibilityStatus.introEligibilityStatusEligible;

    // The user gets the trial text only if BOTH conditions are true
    final showTrialText = hasIntroOffer && isEligibleForTrial;

    String ctaText;
    if (showTrialText) {
      final trialPeriod = product.introductoryPrice!.periodNumberOfUnits;
      // TODO: properly handle the period name
      final periodName = trialPeriod >= 7 ? 'day' : 'week';
      // final periodName = 'week';
      final pluralPeriod = trialPeriod > 1 ? '${periodName}s' : periodName;
      ctaText = 'Try FREE for $trialPeriod $pluralPeriod';
    } else {
      ctaText = 'Continue to Payment';
    }

    return SizedBox(
      width: double.infinity,
      child: OutlinedButton(
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
          backgroundColor: NoejiTheme.colorsOf(context).textPrimary,
          foregroundColor: Theme.of(context).scaffoldBackgroundColor,
          side: BorderSide(
            color: NoejiTheme.colorsOf(context).textPrimary,
            width: 2,
          ),
        ),
        onPressed: _isPurchasing ? null : _purchase,
        child:
            _isPurchasing
                ? SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Theme.of(context).scaffoldBackgroundColor,
                    ),
                  ),
                )
                : Text(
                  ctaText,
                  style: GoogleFonts.afacad(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
      ),
    );
  }

  Widget _buildAutoRenewText() {
    if (_selectedPackage == null) return const SizedBox.shrink();

    final product = _selectedPackage!.storeProduct;

    // Use the same logic from the CTA button
    final hasIntroOffer = product.introductoryPrice != null;
    final eligibility = _introEligibility[product.identifier];
    final isEligibleForTrial =
        eligibility?.status ==
        IntroEligibilityStatus.introEligibilityStatusEligible;
    final showTrialInfo = hasIntroOffer && isEligibleForTrial;

    if (!showTrialInfo) {
      // Determine the billing period
      String period = 'month'; // default
      if (product.identifier.contains('year')) {
        period = 'year';
      } else if (product.identifier.contains('month')) {
        period = 'month';
      } else if (product.identifier.contains('week')) {
        period = 'week';
      }

      return Text(
        'Auto-renews at ${product.priceString} per $period',
        textAlign: TextAlign.center,
        style: GoogleFonts.afacad(
          fontSize: 14,
          color: NoejiTheme.colorsOf(context).textSecondary,
        ),
      );
    }

    return Text(
      'We will remind you one day before the end of the trial.',
      textAlign: TextAlign.center,
      style: GoogleFonts.afacad(
        fontSize: 14,
        color: NoejiTheme.colorsOf(context).textSecondary,
      ),
    );
  }

  Widget _buildFooter() {
    return Wrap(
      alignment: WrapAlignment.center,
      spacing: 8,
      children: [
        TextButton(
          onPressed: _isPurchasing ? null : _restorePurchases,
          child: Text(
            'Restore Purchase',
            style: GoogleFonts.afacad(
              fontSize: 14,
              color: NoejiTheme.colorsOf(context).textSecondary,
            ),
          ),
        ),
        TextButton(
          onPressed: () => _showTermsOfService(context),
          child: Text(
            'Terms',
            style: GoogleFonts.afacad(
              fontSize: 14,
              color: NoejiTheme.colorsOf(context).textSecondary,
            ),
          ),
        ),
        TextButton(
          onPressed: () => _showPrivacyPolicy(context),
          child: Text(
            'Privacy',
            style: GoogleFonts.afacad(
              fontSize: 14,
              color: NoejiTheme.colorsOf(context).textSecondary,
            ),
          ),
        ),
      ],
    );
  }

  /// Show the Terms of Service screen
  void _showTermsOfService(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const TermsOfServiceScreen()),
    );
  }

  /// Show the Privacy Policy screen
  void _showPrivacyPolicy(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const PrivacyPolicyScreen()),
    );
  }

  // Purchase and restore methods
  Future<void> _purchase() async {
    if (_selectedPackage == null) return;

    setState(() => _isPurchasing = true);
    Logger.debug(
      'CustomPaywallScreen: Starting purchase for package: ${_selectedPackage!.identifier}',
    );

    try {
      final customerInfo = await Purchases.purchasePackage(_selectedPackage!);
      Logger.debug(
        'CustomPaywallScreen: Purchase successful for ${customerInfo.originalAppUserId}',
      );

      // Return true to indicate successful purchase
      if (mounted && Navigator.of(context).canPop()) {
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      Logger.error('CustomPaywallScreen: Purchase failed', e);

      // Check if user cancelled the purchase
      final errorString = e.toString().toLowerCase();
      if (errorString.contains('cancelled') || errorString.contains('cancel')) {
        Logger.debug('CustomPaywallScreen: User cancelled purchase');
        // User cancelled, no need to show error
      } else {
        _showErrorSnackBar('Purchase failed. Please try again.');
      }
    } finally {
      if (mounted) {
        setState(() => _isPurchasing = false);
      }
      // Crucial: Refresh the global user state after the transaction.
      Logger.debug(
        'CustomPaywallScreen: Refreshing global user state after purchase attempt.',
      );
      await ref.read(userStateNotifierProvider.notifier).refresh();
    }
  }

  Future<void> _restorePurchases() async {
    setState(() => _isPurchasing = true);
    Logger.debug('CustomPaywallScreen: Starting restore purchases');

    try {
      final customerInfo = await Purchases.restorePurchases();
      Logger.debug('CustomPaywallScreen: Restore purchases successful');

      // Check if any entitlements were restored
      if (customerInfo.entitlements.active.isNotEmpty) {
        _showSuccessSnackBar('Purchases restored successfully!');
        // Return true to indicate successful restoration
        if (mounted && Navigator.of(context).canPop()) {
          Navigator.of(context).pop(true);
        }
      } else {
        _showErrorSnackBar('No previous purchases found to restore.');
      }
    } catch (e) {
      Logger.error('CustomPaywallScreen: Restore purchases failed', e);
      _showErrorSnackBar('Could not restore purchases. Please try again.');
    } finally {
      if (mounted) {
        setState(() => _isPurchasing = false);
      }
      // Crucial: Refresh the global user state after the restore.
      Logger.debug(
        'CustomPaywallScreen: Refreshing global user state after restore attempt.',
      );
      await ref.read(userStateNotifierProvider.notifier).refresh();
    }
  }

  void _showErrorSnackBar(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: GoogleFonts.afacad(color: Colors.white)),
        backgroundColor: NoejiTheme.colorsOf(context).error,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: GoogleFonts.afacad(color: Colors.white)),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _dismissPaywall() {
    Logger.debug('CustomPaywallScreen: User dismissed paywall via X button');
    // Navigate back to the previous screen without changing subscription status
    // This maintains the free tier rate limits and quotas
    // Return false to indicate no purchase was made
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop(false);
    }
  }
}

/// Widget for displaying a single benefit item with checkmark
class _BenefitItem extends StatelessWidget {
  final String primaryText;
  final String secondaryText;

  const _BenefitItem({required this.primaryText, required this.secondaryText});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 2.0),
            child: Icon(Icons.check_circle, color: Colors.green, size: 18),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  primaryText,
                  style: GoogleFonts.afacad(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: NoejiTheme.colorsOf(context).textPrimary,
                  ),
                ),
                if (secondaryText.isNotEmpty) ...[
                  const SizedBox(height: 2),
                  Text(
                    secondaryText,
                    style: GoogleFonts.afacad(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: NoejiTheme.colorsOf(context).textSecondary,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
