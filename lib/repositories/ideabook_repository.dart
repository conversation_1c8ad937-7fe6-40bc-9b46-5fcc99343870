import 'package:noeji/models/models.dart' as models;
import 'package:noeji/repositories/generic_repository.dart';
import 'package:noeji/services/firebase/firestore_service.dart';
import 'package:noeji/services/limits/repository_limits_helper.dart';
import 'package:noeji/services/storage/local_file_storage.dart';
import 'package:noeji/utils/id_utils.dart';

/// Repository for managing Ideabook data
class IdeabookRepository extends GenericRepository<models.Ideabook> {
  /// Firestore service for cloud storage
  final FirestoreService _firestoreService;

  /// Chat file storage for managing chat files
  final ChatFileStorage? _chatFileStorage;

  /// Repository limits helper for checking user limits
  final RepositoryLimitsHelper _limitsHelper;

  /// Creates a new IdeabookRepository
  IdeabookRepository({
    required FirestoreService firestoreService,
    required RepositoryLimitsHelper limitsHelper,
    ChatFileStorage? chatFileStorage,
  }) : _firestoreService = firestoreService,
       _chatFileStorage = chatFileStorage,
       _limitsHelper = limitsHelper,
       super();

  /// Generate a unique ID for a new entity
  String _generateId() {
    return IdUtils.generateId();
  }

  @override
  Future<List<models.Ideabook>> getAll() async {
    logOperation('getAll', details: 'Using Firestore with listener');
    try {
      // Use the listener to get all ideabooks
      return await _firestoreService.listenToIdeabooks().first;
    } catch (e) {
      logOperation('getAll', error: e);
      rethrow;
    }
  }

  /// Get all ideabooks
  Future<List<models.Ideabook>> getAllIdeabooks() async {
    return getAll();
  }

  /// Get a stream of all ideabooks
  Stream<List<models.Ideabook>> getIdeabooksStream() {
    logOperation('getIdeabooksStream', details: 'Using Firestore listener');
    return _firestoreService.listenToIdeabooks();
  }

  @override
  Future<models.Ideabook?> getById(String id) async {
    logOperation('getById', id: id, details: 'Using Firestore with listener');
    try {
      // Use the listener to get the ideabook
      return await _firestoreService.listenToIdeabookById(id).first;
    } catch (e) {
      logOperation('getById', id: id, error: e);
      rethrow;
    }
  }

  /// Get an ideabook by ID
  Future<models.Ideabook?> getIdeabookById(String id) async {
    return getById(id);
  }

  /// Get a stream of an ideabook by ID
  Stream<models.Ideabook?> getIdeabookByIdStream(String id) {
    logOperation(
      'getIdeabookByIdStream',
      id: id,
      details: 'Using Firestore listener',
    );
    return _firestoreService.listenToIdeabookById(id);
  }

  @override
  Future<models.Ideabook> create(Map<String, dynamic> data) async {
    logOperation('create');
    try {
      final name = data['name'] as String;
      final color =
          data['color'] as models.IdeabookColor? ?? models.IdeabookColor.none;
      final isLocked = data['isLocked'] as bool? ?? false;
      final showAsTodoList = data['showAsTodoList'] as bool? ?? false;

      // Create the ideabook model
      final now = DateTime.now();
      final ideabook = models.Ideabook(
        id: _generateId(),
        name: name,
        color: color,
        isLocked: isLocked,
        showAsTodoList: showAsTodoList,
        createdAt: now,
        updatedAt: now,
      );

      return await _firestoreService.createIdeabook(ideabook);
    } catch (e) {
      logOperation('create', error: e);
      rethrow;
    }
  }

  /// Check if the user has reached the maximum number of ideabooks
  Future<bool> isUserAtIdeabookLimit() async {
    logOperation('isUserAtIdeabookLimit');
    try {
      final ideabooks = await getAllIdeabooks();
      final count = ideabooks.length;
      final maxIdeabooks = await _limitsHelper.getMaxIdeabooks();
      final isAtLimit = count >= maxIdeabooks;
      logOperation(
        'isUserAtIdeabookLimit',
        details: 'Count: $count, Max: $maxIdeabooks, Is at limit: $isAtLimit',
      );
      return isAtLimit;
    } catch (e) {
      logOperation('isUserAtIdeabookLimit', error: e);
      // In case of error, assume not at limit to avoid blocking the user
      return false;
    }
  }

  /// Create a new ideabook
  Future<models.Ideabook> createIdeabook({
    required String name,
    models.IdeabookColor color = models.IdeabookColor.none,
    bool isLocked = false,
    bool showAsTodoList = false,
  }) async {
    logOperation('createIdeabook', details: 'Using Firestore');

    // Check if the user has reached the maximum number of ideabooks
    final isAtLimit = await isUserAtIdeabookLimit();
    if (isAtLimit) {
      await _limitsHelper.throwIdeabookLimitException();
    }

    return create({
      'name': name, 
      'color': color, 
      'isLocked': isLocked,
      'showAsTodoList': showAsTodoList,
    });
  }

  @override
  Future<bool> update(models.Ideabook entity) async {
    logOperation('update', id: entity.id);
    try {
      return await _firestoreService.updateIdeabook(entity);
    } catch (e) {
      logOperation('update', id: entity.id, error: e);
      rethrow;
    }
  }

  /// Update an existing ideabook
  Future<bool> updateIdeabook(models.Ideabook ideabook) async {
    return update(ideabook);
  }

  @override
  Future<bool> delete(String id) async {
    logOperation('delete', id: id);
    try {
      // Delete the chat file for this ideabook if it exists
      if (_chatFileStorage != null) {
        try {
          final chatDeleted = await _chatFileStorage.deleteChat(id);
          logOperation(
            'delete',
            id: id,
            details: 'Chat file deleted: $chatDeleted',
          );
        } catch (e) {
          logOperation(
            'delete',
            id: id,
            details: 'Failed to delete chat file',
            error: e,
          );
          // Continue with deletion even if chat file deletion fails
        }
      }

      return await _firestoreService.deleteIdeabook(id);
    } catch (e) {
      logOperation('delete', id: id, error: e);
      rethrow;
    }
  }

  /// Delete an ideabook and all associated data (ideas, notes, chats)
  Future<bool> deleteIdeabook(String id) async {
    return delete(id);
  }

  /// Toggle the lock state of an ideabook
  Future<bool> toggleIdeabookLock(String id) async {
    logOperation('toggleIdeabookLock', id: id);

    try {
      // Get the ideabook
      final ideabook = await getIdeabookById(id);
      if (ideabook == null) {
        logOperation(
          'toggleIdeabookLock',
          id: id,
          details: 'Ideabook not found',
        );
        return false;
      }

      // Toggle the lock state
      final updatedIdeabook = ideabook.copyWith(
        isLocked: !ideabook.isLocked,
        updatedAt: DateTime.now(),
      );

      // Update the ideabook
      final success = await updateIdeabook(updatedIdeabook);
      logOperation(
        'toggleIdeabookLock',
        id: id,
        details: 'Lock state toggled to ${updatedIdeabook.isLocked}',
      );
      return success;
    } catch (e) {
      logOperation('toggleIdeabookLock', id: id, error: e);
      rethrow;
    }
  }

  /// Count the total number of ideabooks
  Future<int> countIdeabooks() async {
    logOperation('countIdeabooks');
    try {
      final ideabooks = await getAllIdeabooks();
      final count = ideabooks.length;
      logOperation('countIdeabooks', details: 'Found $count ideabooks');
      return count;
    } catch (e) {
      logOperation('countIdeabooks', error: e);
      rethrow;
    }
  }
}
