import 'package:package_info_plus/package_info_plus.dart';

class FeedbackModel {
  final int rating;
  final String content;
  final String? userId;
  final String? appVersion;
  final DateTime timestamp;

  FeedbackModel({
    required this.rating,
    required this.content,
    this.userId,
    this.appVersion,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  Map<String, dynamic> toFirestore() {
    return {
      'u': userId ?? '',
      'v': appVersion ?? '',
      'r': rating,
      'c': content,
    };
  }

  static Future<FeedbackModel> create({
    required int rating,
    required String content,
    String? userId,
  }) async {
    final packageInfo = await PackageInfo.fromPlatform();
    return FeedbackModel(
      rating: rating,
      content: content,
      userId: userId,
      appVersion: packageInfo.version,
    );
  }
}