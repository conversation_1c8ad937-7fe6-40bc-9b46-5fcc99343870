import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:noeji/utils/logger.dart';

/// Utility class for sharing files using native OS sharing
class FileExportUtil {
  /// Share a markdown file with the given content
  static Future<bool> shareMarkdownFile(String content, {String prefix = 'ideabooks'}) async {
    try {
      Logger.debug('Starting markdown file share');
      
      // Get the current timestamp for filename
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filename = '${prefix}_export_$timestamp.md';
      
      // Create temporary file
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/$filename');
      await tempFile.writeAsString(content);
      
      // Share the file using native OS sharing
      final xFile = XFile(tempFile.path);
      await Share.shareXFiles(
        [xFile],
        subject: 'Noeji Export - Markdown',
        text: 'Here is my exported data from Noeji app.',
      );
      
      Logger.debug('Markdown file share completed');
      return true;
    } catch (e) {
      Logger.error('Failed to share markdown file: $e');
      return false;
    }
  }
  
  /// Share an HTML file with the given content
  static Future<bool> shareHtmlFile(String content, {String prefix = 'ideabooks'}) async {
    try {
      Logger.debug('Starting HTML file share');
      
      // Get the current timestamp for filename
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filename = '${prefix}_export_$timestamp.html';
      
      // Create temporary file
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/$filename');
      await tempFile.writeAsString(content);
      
      // Share the file using native OS sharing
      final xFile = XFile(tempFile.path);
      await Share.shareXFiles(
        [xFile],
        subject: 'Noeji Export - HTML',
        text: 'Here is my exported data from Noeji app.',
      );
      
      Logger.debug('HTML file share completed');
      return true;
    } catch (e) {
      Logger.error('Failed to share HTML file: $e');
      return false;
    }
  }

  /// Legacy method names for backward compatibility
  @Deprecated('Use shareMarkdownFile instead')
  static Future<bool> exportMarkdownFile(String content, {String prefix = 'ideabooks'}) async {
    return shareMarkdownFile(content, prefix: prefix);
  }
  
  @Deprecated('Use shareHtmlFile instead')
  static Future<bool> exportHtmlFile(String content, {String prefix = 'ideabooks'}) async {
    return shareHtmlFile(content, prefix: prefix);
  }
}