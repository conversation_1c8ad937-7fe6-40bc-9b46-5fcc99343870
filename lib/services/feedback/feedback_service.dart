import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'dart:typed_data';
import 'dart:convert';
import '../../models/feedback_model.dart';
import '../../utils/logger.dart';
import '../auth/auth_service.dart';
import '../auth/auth_providers.dart';

final feedbackServiceProvider = Provider<FeedbackService>((ref) {
  return FeedbackService(
    authService: ref.read(authServiceProvider),
  );
});

/// Provider for feedback trigger callback
/// This provides a method to check and potentially trigger feedback on app start
final feedbackTriggerProvider = Provider<FeedbackTrigger>((ref) {
  return FeedbackTrigger(feedbackService: ref.read(feedbackServiceProvider));
});

/// Helper class for handling feedback triggers on app start/reload
class FeedbackTrigger {
  final FeedbackService feedbackService;
  
  FeedbackTrigger({required this.feedbackService});
  
  /// Fire-and-forget check for feedback trigger on app start/reload
  /// This method doesn't block and returns immediately
  void checkAndTriggerFeedback(Function(bool) onResult) {
    // Run asynchronously without blocking app startup
    _checkFeedbackAsync(onResult);
  }
  
  /// Internal async method that does the actual checking
  void _checkFeedbackAsync(Function(bool) onResult) async {
    try {
      // Small delay to ensure app is fully loaded
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Check if feedback should be shown
      final shouldShow = await feedbackService.shouldShowFeedbackPanel();
      
      // Call the callback with the result
      onResult(shouldShow);
      
    } catch (e) {
      Logger.error('FeedbackTrigger: Error checking feedback trigger: $e', e);
      onResult(false);
    }
  }
}

class FeedbackService {
  final AuthService _authService;
  
  static const String _feedbackShownKey = 'feedback_panel_shown';
  static const String _cachedImageKey = 'cached_feedback_image';
  static const String _feedbackTriggerHistoryKey = 'feedback_trigger_history';
  
  String _heroImageUrl = '';
  String _message = '';
  Uint8List? _cachedImageData;
  
  FeedbackService({required AuthService authService}) : _authService = authService;

  String get heroImageUrl => _heroImageUrl;
  String get message => _message;
  Uint8List? get cachedImageData => _cachedImageData;

  /// Initialize the feedback service and preload assets
  Future<void> initialize() async {
    try {
      await _loadRemoteConfig();
      await _preloadAssets();
    } catch (e) {
      Logger.error('Error initializing feedback service: $e', e);
    }
  }

  /// Load remote config values
  Future<void> _loadRemoteConfig() async {
    try {
      final remoteConfig = FirebaseRemoteConfig.instance;
      await remoteConfig.fetchAndActivate();
      
      _heroImageUrl = remoteConfig.getString('feedback_panel_hero_image_url');
      _message = remoteConfig.getString('feedback_panel_message');
      
      Logger.debug('FeedbackService: Loaded feedback config - Image URL: $_heroImageUrl, Message: $_message');
    } catch (e) {
      Logger.error('FeedbackService: Error loading remote config: $e', e);
      // Set defaults if remote config fails
      _heroImageUrl = '';
      _message = '# How are we doing?\n\nWe\'d love to hear your thoughts! 💭';
    }
  }

  /// Preload and cache the hero image
  Future<void> _preloadAssets() async {
    if (_heroImageUrl.isEmpty) return;
    
    try {
      // Check if image is already cached
      final prefs = await SharedPreferences.getInstance();
      final cachedImageBytes = prefs.getString(_cachedImageKey);
      
      if (cachedImageBytes != null) {
        // Use cached image
        _cachedImageData = Uint8List.fromList(cachedImageBytes.codeUnits);
        Logger.debug('FeedbackService: Using cached feedback image');
        return;
      }
      
      // Download and cache the image
      final response = await http.get(Uri.parse(_heroImageUrl));
      if (response.statusCode == 200) {
        _cachedImageData = response.bodyBytes;
        
        // Cache the image data
        await prefs.setString(_cachedImageKey, String.fromCharCodes(_cachedImageData!));
        Logger.debug('FeedbackService: Cached feedback image successfully');
      } else {
        Logger.error('FeedbackService: Failed to download feedback image: ${response.statusCode}');
      }
    } catch (e) {
      Logger.error('FeedbackService: Error preloading feedback image: $e', e);
    }
  }

  /// Check if feedback panel should be shown automatically
  /// 
  /// [isManualTrigger] - true if this is a manual trigger (from context menu), false for automatic
  /// [forceShow] - bypass impression limits and show anyway (for testing)
  /// 
  /// Returns true if the panel should be shown, false otherwise
  Future<bool> shouldShowFeedbackPanel({
    bool isManualTrigger = false,
    bool forceShow = false,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final remoteConfig = FirebaseRemoteConfig.instance;
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      
      // For manual triggers, always show unless force disabled
      if (isManualTrigger) {
        Logger.debug('FeedbackService: Manual feedback trigger - showing panel');
        await _trackFeedbackImpression(isManualTrigger: true);
        return true;
      }
      
      // For force show (testing), bypass all checks
      if (forceShow) {
        Logger.debug('FeedbackService: Force showing feedback panel');
        await _trackFeedbackImpression(isManualTrigger: false, isForced: true);
        return true;
      }
      
      // Check if automatic feedback has already been shown (only once per app lifecycle)
      final hasShownAutoFeedback = prefs.getBool(_feedbackShownKey) ?? false;
      if (hasShownAutoFeedback) {
        Logger.debug('FeedbackService: Automatic feedback has already been shown');
        return false;
      }
      
      // Check if enough time has passed since cold start
      final coldStartSeconds = remoteConfig.getInt('feedback_panel_trigger_cold_start_seconds');
      final defaultColdStartSeconds = 172800; // fallback: 2 days.
      final actualColdStartSeconds = coldStartSeconds > 0 ? coldStartSeconds : defaultColdStartSeconds;
      
      final appStartTime = prefs.getInt('app_start_time') ?? 0;
      
      if (appStartTime == 0) {
        // First time - set the start time
        await prefs.setInt('app_start_time', currentTime);
        Logger.debug('FeedbackService: First app start - setting start time');
        return false;
      }
      
      final elapsedSeconds = (currentTime - appStartTime) ~/ 1000;
      if (elapsedSeconds < actualColdStartSeconds) {
        Logger.debug('FeedbackService: Not enough time since cold start: ${elapsedSeconds}s/${actualColdStartSeconds}s');
        return false;
      }
      
      // All checks passed - show the panel
      Logger.debug('FeedbackService: Automatic feedback trigger conditions met - showing panel');
      await _trackFeedbackImpression(isManualTrigger: false);
      return true;
      
    } catch (e) {
      Logger.error('FeedbackService: Error checking feedback panel trigger: $e', e);
      return false;
    }
  }
  
  /// Track feedback panel impression with analytics
  Future<void> _trackFeedbackImpression({
    required bool isManualTrigger,
    bool isForced = false,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      
      // Mark automatic feedback as shown (only for automatic triggers)
      if (!isManualTrigger) {
        await prefs.setBool(_feedbackShownKey, true);
        Logger.debug('FeedbackService: Marked automatic feedback as shown');
      }
      
      // Track trigger history for analytics (simplified)
      final historyJson = prefs.getString(_feedbackTriggerHistoryKey) ?? '[]';
      final history = List<Map<String, dynamic>>.from(
        (jsonDecode(historyJson) as List).cast<Map<String, dynamic>>(),
      );
      
      final triggerData = {
        'timestamp': currentTime,
        'type': isManualTrigger ? 'manual' : 'automatic',
        'isForced': isForced,
      };
      
      history.add(triggerData);
      
      // Keep only last 10 entries to avoid storage bloat
      if (history.length > 10) {
        history.removeRange(0, history.length - 10);
      }
      
      await prefs.setString(_feedbackTriggerHistoryKey, jsonEncode(history));
      
      Logger.debug('FeedbackService: Feedback trigger tracked: $triggerData');
      
    } catch (e) {
      Logger.error('Error tracking feedback impression: $e', e);
    }
  }

  /// Mark feedback panel as shown
  Future<void> markFeedbackPanelShown() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_feedbackShownKey, true);
      Logger.debug('Marked feedback panel as shown');
    } catch (e) {
      Logger.error('Error marking feedback panel as shown: $e', e);
    }
  }

  /// Submit feedback to Firestore
  Future<void> submitFeedback(FeedbackModel feedback) async {
    try {
      final user = _authService.currentUser;
      
      final feedbackWithUser = FeedbackModel(
        rating: feedback.rating,
        content: feedback.content,
        userId: user?.uid ?? '',
        appVersion: feedback.appVersion,
        timestamp: feedback.timestamp,
      );
      
      final firestore = FirebaseFirestore.instance;
      final docId = feedbackWithUser.timestamp.millisecondsSinceEpoch.toString();
      
      await firestore
          .collection('feedbacks')
          .doc(docId)
          .set(feedbackWithUser.toFirestore());
      
      Logger.debug('Feedback submitted successfully: $docId');
      
      // Mark panel as shown after successful submission
      await markFeedbackPanelShown();
    } catch (e) {
      Logger.error('Error submitting feedback: $e', e);
      // Still mark as shown even if submission fails to prevent repeated prompts
      await markFeedbackPanelShown();
    }
  }

  /// Get feedback analytics and statistics
  Future<Map<String, dynamic>> getFeedbackAnalytics() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final hasShownAutoFeedback = prefs.getBool(_feedbackShownKey) ?? false;
      final historyJson = prefs.getString(_feedbackTriggerHistoryKey) ?? '[]';
      
      final history = List<Map<String, dynamic>>.from(
        (jsonDecode(historyJson) as List).cast<Map<String, dynamic>>(),
      );
      
      final analytics = {
        'hasShownAutoFeedback': hasShownAutoFeedback,
        'triggerHistory': history,
        'automaticTriggers': history.where((h) => h['type'] == 'automatic').length,
        'manualTriggers': history.where((h) => h['type'] == 'manual').length,
      };
      
      Logger.debug('Feedback analytics retrieved: $analytics');
      return analytics;
      
    } catch (e) {
      Logger.error('Error getting feedback analytics: $e', e);
      return {};
    }
  }
  
  /// Check if feedback panel can be shown (without triggering impression tracking)
  /// This is useful for UI state management
  Future<bool> canShowFeedbackPanel() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Check if automatic feedback has already been shown
      final hasShownAutoFeedback = prefs.getBool(_feedbackShownKey) ?? false;
      return !hasShownAutoFeedback;
      
    } catch (e) {
      Logger.error('FeedbackService: Error checking if feedback panel can be shown: $e', e);
      return false;
    }
  }
  
  /// Reset feedback state (for testing purposes)
  Future<void> resetFeedbackState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_feedbackShownKey);
      await prefs.remove(_cachedImageKey);
      await prefs.remove(_feedbackTriggerHistoryKey);
      await prefs.remove('app_start_time');
      Logger.debug('Feedback state reset');
    } catch (e) {
      Logger.error('Error resetting feedback state: $e', e);
    }
  }
  
  /// Test method to verify feedback panel logic
  Future<void> testFeedbackPanelLogic() async {
    Logger.debug('=== Testing Feedback Panel Logic ===');
    
    // Test 1: Check initial state
    final canShow = await canShowFeedbackPanel();
    Logger.debug('Can show feedback panel (initial): $canShow');
    
    // Test 2: Test automatic trigger
    final shouldShowAuto = await shouldShowFeedbackPanel(isManualTrigger: false);
    Logger.debug('Should show feedback panel (automatic): $shouldShowAuto');
    
    // Test 3: Test manual trigger
    final shouldShowManual = await shouldShowFeedbackPanel(isManualTrigger: true);
    Logger.debug('Should show feedback panel (manual): $shouldShowManual');
    
    // Test 4: Test force show
    final shouldShowForced = await shouldShowFeedbackPanel(forceShow: true);
    Logger.debug('Should show feedback panel (forced): $shouldShowForced');
    
    // Test 5: Get analytics
    final analytics = await getFeedbackAnalytics();
    Logger.debug('Feedback analytics: $analytics');
    
    Logger.debug('=== Feedback Panel Logic Test Complete ===');
  }
}