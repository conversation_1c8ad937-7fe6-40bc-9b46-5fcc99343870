import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/services/rating/rating_service_provider.dart';
import 'package:noeji/ui/providers/recording_mode_provider.dart';
import 'package:noeji/utils/logger.dart';

/// Provider that listens to app state changes and updates the rating service
final ratingStateListenerProvider = Provider((ref) {
  return RatingStateListener(ref);
});

/// Listens to various app state changes and updates the rating service
class RatingStateListener {
  final Ref ref;

  RatingStateListener(this.ref) {
    _setupListeners();
  }

  void _setupListeners() {
    // Listen to recording mode changes
    ref.listen<bool>(recordingModeProvider, (previous, next) {
      final ratingService = ref.read(inAppRatingServiceProvider);
      ratingService.setRecordingState(next);
      Logger.debug('RatingStateListener: Recording state changed to $next');
    });

    // Listen to recording mode state changes (more detailed)
    ref.listen<RecordingModeState>(recordingModeStateProvider, (previous, next) {
      final ratingService = ref.read(inAppRatingServiceProvider);
      final isRecording = next == RecordingModeState.recording || 
                         next == RecordingModeState.processing;
      ratingService.setRecordingState(isRecording);
      Logger.debug('RatingStateListener: Recording mode state changed to $next, isRecording: $isRecording');
    });

    // Note: For editing states, we would need to check if there are global editing providers
    // or we might need to track editing state differently since editing is often local to specific items
    Logger.debug('RatingStateListener: State listeners initialized');
  }
}