import 'dart:async';

import 'package:in_app_review/in_app_review.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:noeji/services/remote_config/remote_config_service.dart';
import 'package:noeji/utils/logger.dart';
import 'package:noeji/repositories/ideabook_repository.dart';
import 'package:noeji/repositories/idea_repository.dart';

/// Service for handling in-app rating prompts
/// Follows Apple's guidelines and only uses the official SKStoreReviewController API
/// 
/// Remote Config Parameters:
/// - in_app_rating_trigger_min_ideabooks: Minimum ideabooks required (default: 2)
/// - in_app_rating_trigger_min_ideas: Minimum ideas required (default: 10)
/// - in_app_rating_trigger_paywall_cooldown_seconds: Wait time after paywall (default: 3600 = 1 hour)
/// - in_app_rating_trigger_rate_limit_seconds: Min time between prompts (default: 3600 = 1 hour)
/// - in_app_rating_trigger_cold_start_seconds: Min app usage time before first prompt (default: 86400 = 1 day)
class InAppRatingService {
  static InAppRatingService? _instance;
  static InAppRatingService get instance => _instance ??= InAppRatingService._();

  InAppRatingService._() {
    // Initialize first app use time on service creation
    _initializeFirstAppUseTime();
  }

  // Storage keys
  static const _keyLastPaywallShownTime = 'last_paywall_shown_time';
  static const _keyLastRatingRequestTime = 'last_rating_request_time';
  static const _keyFirstAppUseTime = 'first_app_use_time';
  
  // Remote config keys for thresholds and timing
  static const _remoteConfigKeyMinIdeabooks = 'in_app_rating_trigger_min_ideabooks';
  static const _remoteConfigKeyMinIdeas = 'in_app_rating_trigger_min_ideas';
  static const _remoteConfigKeyPaywallCooldown = 'in_app_rating_trigger_paywall_cooldown_seconds';
  static const _remoteConfigKeyRateLimit = 'in_app_rating_trigger_rate_limit_seconds';
  static const _remoteConfigKeyColdStart = 'in_app_rating_trigger_cold_start_seconds';
  
  // Default values (fallback)
  static const _defaultMinIdeabooks = 2;
  static const _defaultMinIdeas = 10;
  static const _defaultPaywallCooldownSeconds = 3600; // 1 hour
  static const _defaultRateLimitSeconds = 3600; // 1 hour
  static const _defaultColdStartSeconds = 86400; // 1 day

  final InAppReview _inAppReview = InAppReview.instance;
  final RemoteConfigService _remoteConfigService = RemoteConfigService.instance;

  // Flags to track user state
  bool _userIsRecording = false;
  bool _userIsEditing = false;
  bool _contextMenuIsOpen = false;
  bool _isProcessingTrigger = false;

  /// Update recording state
  void setRecordingState(bool isRecording) {
    _userIsRecording = isRecording;
    Logger.debug('InAppRatingService: Recording state updated to $isRecording');
  }

  /// Update editing state
  void setEditingState(bool isEditing) {
    _userIsEditing = isEditing;
    Logger.debug('InAppRatingService: Editing state updated to $isEditing');
  }

  /// Update context menu state
  void setContextMenuState(bool isOpen) {
    _contextMenuIsOpen = isOpen;
    Logger.debug('InAppRatingService: Context menu state updated to $isOpen');
  }

  /// Initialize first app use time if not already set
  Future<void> _initializeFirstAppUseTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final existingTime = prefs.getInt(_keyFirstAppUseTime);
      
      if (existingTime == null) {
        final now = DateTime.now().millisecondsSinceEpoch;
        await prefs.setInt(_keyFirstAppUseTime, now);
        Logger.debug('InAppRatingService: First app use time initialized at timestamp: $now (${DateTime.fromMillisecondsSinceEpoch(now)})');
      } else {
        Logger.debug('InAppRatingService: First app use time already exists: $existingTime (${DateTime.fromMillisecondsSinceEpoch(existingTime)})');
      }
    } catch (e) {
      Logger.error('InAppRatingService: Failed to initialize first app use time', e);
    }
  }

  /// Record when paywall was shown to user
  Future<void> recordPaywallShown() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now().millisecondsSinceEpoch;
      await prefs.setInt(_keyLastPaywallShownTime, now);
      Logger.debug('InAppRatingService: Paywall shown time recorded at timestamp: $now (${DateTime.fromMillisecondsSinceEpoch(now)})');
    } catch (e) {
      Logger.error('InAppRatingService: Failed to record paywall shown time', e);
    }
  }

  /// Check if user saw paywall recently (within cooldown period)
  Future<bool> _userSawPaywallRecently() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastPaywallTime = prefs.getInt(_keyLastPaywallShownTime);
      
      if (lastPaywallTime == null) {
        return false;
      }
      
      // Get cooldown period from remote config
      final cooldownSeconds = _remoteConfigService.getInt(_remoteConfigKeyPaywallCooldown);
      final effectiveCooldown = cooldownSeconds > 0 ? cooldownSeconds : _defaultPaywallCooldownSeconds;
      
      final now = DateTime.now();
      final lastPaywallDateTime = DateTime.fromMillisecondsSinceEpoch(lastPaywallTime);
      final secondsSincePaywall = now.difference(lastPaywallDateTime).inSeconds;
      
      final recentlyShown = secondsSincePaywall < effectiveCooldown;
      Logger.debug('InAppRatingService: Last paywall: $lastPaywallDateTime, Current time: $now, Seconds since paywall: $secondsSincePaywall, cooldown: $effectiveCooldown, recently shown: $recentlyShown');
      
      return recentlyShown;
    } catch (e) {
      Logger.error('InAppRatingService: Failed to check paywall time', e);
      return false; // Assume not recently shown if we can't check
    }
  }

  /// Check if user is currently busy/active
  bool _userIsCurrentlyBusy() {
    final busy = _userIsRecording || _userIsEditing || _contextMenuIsOpen;
    Logger.debug('InAppRatingService: User busy state - recording: $_userIsRecording, editing: $_userIsEditing, context menu: $_contextMenuIsOpen, overall busy: $busy');
    return busy;
  }

  /// Get minimum thresholds from Firebase Remote Config
  Future<({int minIdeabooks, int minIdeas})> _getThresholds() async {
    try {
      final minIdeabooks = _remoteConfigService.getInt(_remoteConfigKeyMinIdeabooks);
      final minIdeas = _remoteConfigService.getInt(_remoteConfigKeyMinIdeas);
      
      // Use defaults if remote config returns 0 (which might indicate unset values)
      final effectiveMinIdeabooks = minIdeabooks > 0 ? minIdeabooks : _defaultMinIdeabooks;
      final effectiveMinIdeas = minIdeas > 0 ? minIdeas : _defaultMinIdeas;
      
      Logger.debug('InAppRatingService: Retrieved thresholds - ideabooks: $effectiveMinIdeabooks, ideas: $effectiveMinIdeas');
      
      return (minIdeabooks: effectiveMinIdeabooks, minIdeas: effectiveMinIdeas);
    } catch (e) {
      Logger.error('InAppRatingService: Failed to get thresholds from remote config', e);
      return (minIdeabooks: _defaultMinIdeabooks, minIdeas: _defaultMinIdeas);
    }
  }

  /// Check if user meets the minimum content thresholds
  Future<bool> _userMeetsContentThresholds(
    IdeabookRepository ideabookRepository,
    IdeaRepository ideaRepository,
  ) async {
    try {
      final thresholds = await _getThresholds();
      
      // Count ideabooks
      final ideabookCount = await ideabookRepository.countIdeabooks();
      Logger.debug('InAppRatingService: User has $ideabookCount ideabooks, needs ${thresholds.minIdeabooks}');
      
      if (ideabookCount < thresholds.minIdeabooks) {
        return false;
      }
      
      // Count total ideas across all ideabooks
      final totalIdeas = await _countAllIdeas(ideabookRepository, ideaRepository);
      Logger.debug('InAppRatingService: User has $totalIdeas total ideas, needs ${thresholds.minIdeas}');
      
      if (totalIdeas < thresholds.minIdeas) {
        return false;
      }
      
      Logger.debug('InAppRatingService: User meets content thresholds');
      return true;
    } catch (e) {
      Logger.error('InAppRatingService: Failed to check content thresholds', e);
      return false; // Don't show rating if we can't verify thresholds
    }
  }

  /// Count all ideas across all ideabooks
  Future<int> _countAllIdeas(
    IdeabookRepository ideabookRepository,
    IdeaRepository ideaRepository,
  ) async {
    try {
      final ideabooks = await ideabookRepository.getAllIdeabooks();
      int totalIdeas = 0;
      
      for (final ideabook in ideabooks) {
        final ideas = await ideaRepository.getIdeasByIdeabookId(ideabook.id);
        totalIdeas += ideas.length;
      }
      
      return totalIdeas;
    } catch (e) {
      Logger.error('InAppRatingService: Failed to count all ideas', e);
      return 0;
    }
  }

  /// Check if system is available for rating request
  Future<bool> _isSystemAvailable() async {
    try {
      final available = await _inAppReview.isAvailable();
      Logger.debug('InAppRatingService: System available for rating: $available');
      return available;
    } catch (e) {
      Logger.error('InAppRatingService: Failed to check system availability', e);
      return false;
    }
  }

  /// Check if rate limit allows showing a prompt now
  Future<bool> _rateLimitAllowsPrompt() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastRequestTime = prefs.getInt(_keyLastRatingRequestTime);
      
      if (lastRequestTime == null) {
        Logger.debug('InAppRatingService: No previous rating request found, rate limit OK');
        return true;
      }
      
      // Get rate limit from remote config
      final rateLimitSeconds = _remoteConfigService.getInt(_remoteConfigKeyRateLimit);
      final effectiveRateLimit = rateLimitSeconds > 0 ? rateLimitSeconds : _defaultRateLimitSeconds;
      
      final now = DateTime.now();
      final lastRequestDateTime = DateTime.fromMillisecondsSinceEpoch(lastRequestTime);
      final secondsSinceLastRequest = now.difference(lastRequestDateTime).inSeconds;
      
      final rateLimitPassed = secondsSinceLastRequest >= effectiveRateLimit;
      Logger.debug('InAppRatingService: Seconds since last request: $secondsSinceLastRequest, rate limit: $effectiveRateLimit, rate limit passed: $rateLimitPassed');
      
      return rateLimitPassed;
    } catch (e) {
      Logger.error('InAppRatingService: Failed to check rate limit', e);
      return true; // Allow prompt if we can't check (fail open)
    }
  }

  /// Check if cold start period has passed (user has been using app long enough)
  Future<bool> _coldStartPeriodPassed() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final firstAppUseTime = prefs.getInt(_keyFirstAppUseTime);
      
      if (firstAppUseTime == null) {
        Logger.debug('InAppRatingService: No first app use time found, cold start period not passed');
        return false;
      }
      
      // Get cold start period from remote config
      final coldStartSeconds = _remoteConfigService.getInt(_remoteConfigKeyColdStart);
      final effectiveColdStart = coldStartSeconds > 0 ? coldStartSeconds : _defaultColdStartSeconds;
      
      final now = DateTime.now();
      final firstUseDateTime = DateTime.fromMillisecondsSinceEpoch(firstAppUseTime);
      final secondsSinceFirstUse = now.difference(firstUseDateTime).inSeconds;
      
      final coldStartPassed = secondsSinceFirstUse >= effectiveColdStart;
      Logger.debug('InAppRatingService: First use: $firstUseDateTime, Current time: $now, Seconds since first use: $secondsSinceFirstUse, cold start period: $effectiveColdStart, cold start passed: $coldStartPassed');
      
      return coldStartPassed;
    } catch (e) {
      Logger.error('InAppRatingService: Failed to check cold start period', e);
      return false; // Don't allow prompt if we can't check (fail closed for new users)
    }
  }

  /// Check all conditions for showing rating prompt
  Future<bool> _shouldShowRatingPrompt(
    IdeabookRepository ideabookRepository,
    IdeaRepository ideaRepository,
  ) async {
    try {
      Logger.debug('InAppRatingService: Checking rating prompt conditions...');
      
      // Check if user is busy
      if (_userIsCurrentlyBusy()) {
        Logger.debug('InAppRatingService: User is busy, not showing rating');
        return false;
      }
      
      // Check if paywall was shown recently
      if (await _userSawPaywallRecently()) {
        Logger.debug('InAppRatingService: User saw paywall recently, not showing rating');
        return false;
      }
      
      // Check rate limit
      if (!await _rateLimitAllowsPrompt()) {
        Logger.debug('InAppRatingService: Rate limit prevents showing rating prompt');
        return false;
      }
      
      // Check cold start period (user must have been using app long enough)
      if (!await _coldStartPeriodPassed()) {
        Logger.debug('InAppRatingService: Cold start period not passed, not showing rating');
        return false;
      }
      
      // Check content thresholds
      if (!await _userMeetsContentThresholds(ideabookRepository, ideaRepository)) {
        Logger.debug('InAppRatingService: User does not meet content thresholds');
        return false;
      }
      
      // Check system availability
      if (!await _isSystemAvailable()) {
        Logger.debug('InAppRatingService: System not available for rating');
        return false;
      }
      
      Logger.debug('InAppRatingService: All conditions met for showing rating prompt');
      return true;
    } catch (e) {
      Logger.error('InAppRatingService: Error checking rating conditions', e);
      return false;
    }
  }

  /// Trigger rating prompt if conditions are met
  /// This is a fire-and-forget operation that won't block the UI
  Future<void> tryTriggerRatingPrompt(
    IdeabookRepository ideabookRepository,
    IdeaRepository ideaRepository,
  ) async {
    // Prevent multiple simultaneous triggers
    if (_isProcessingTrigger) {
      Logger.debug('InAppRatingService: Already processing trigger, skipping');
      return;
    }
    
    _isProcessingTrigger = true;
    
    try {
      Logger.debug('InAppRatingService: Attempting to trigger rating prompt...');
      
      if (!await _shouldShowRatingPrompt(ideabookRepository, ideaRepository)) {
        Logger.debug('InAppRatingService: Conditions not met, not showing rating prompt');
        return;
      }
      
      // Record the request time (for potential future rate limiting)
      await _recordRatingRequestTime();
      
      // Request the rating - this is fire-and-forget
      // We don't know if the prompt will actually be shown (iOS decides)
      _inAppReview.requestReview();
      
      Logger.debug('InAppRatingService: Rating review requested (system will decide whether to show)');
      
    } catch (e) {
      Logger.error('InAppRatingService: Error triggering rating prompt', e);
    } finally {
      _isProcessingTrigger = false;
    }
  }

  /// Record when we made a rating request
  Future<void> _recordRatingRequestTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now().millisecondsSinceEpoch;
      await prefs.setInt(_keyLastRatingRequestTime, now);
      Logger.debug('InAppRatingService: Rating request time recorded');
    } catch (e) {
      Logger.error('InAppRatingService: Failed to record rating request time', e);
    }
  }

  /// Trigger rating after creating a new ideabook
  Future<void> onIdeabookCreated(
    IdeabookRepository ideabookRepository,
    IdeaRepository ideaRepository,
  ) async {
    Logger.debug('InAppRatingService: Ideabook created, starting 2-second delay before checking rating trigger');
    
    // Delay to ensure recording activity is fully completed
    await Future.delayed(const Duration(seconds: 2));
    
    Logger.debug('InAppRatingService: 2-second delay completed, now checking rating trigger for ideabook creation');
    
    // Fire and forget - don't await to avoid blocking the UI
    unawaited(tryTriggerRatingPrompt(ideabookRepository, ideaRepository));
  }

  /// Trigger rating after creating a new idea
  Future<void> onIdeaCreated(
    IdeabookRepository ideabookRepository,
    IdeaRepository ideaRepository,
  ) async {
    Logger.debug('InAppRatingService: Idea created, starting 2-second delay before checking rating trigger');
    
    // Delay to ensure recording activity is fully completed
    await Future.delayed(const Duration(seconds: 2));
    
    Logger.debug('InAppRatingService: 2-second delay completed, now checking rating trigger for idea creation');
    
    // Fire and forget - don't await to avoid blocking the UI
    unawaited(tryTriggerRatingPrompt(ideabookRepository, ideaRepository));
  }

  /// Trigger rating after saving a new note
  Future<void> onNoteSaved(
    IdeabookRepository ideabookRepository,
    IdeaRepository ideaRepository,
  ) async {
    Logger.debug('InAppRatingService: Note saved, starting 2-second delay before checking rating trigger');
    
    // Delay to ensure any ongoing activity is fully completed
    await Future.delayed(const Duration(seconds: 2));
    
    Logger.debug('InAppRatingService: 2-second delay completed, now checking rating trigger for note saving');
    
    // Fire and forget - don't await to avoid blocking the UI
    unawaited(tryTriggerRatingPrompt(ideabookRepository, ideaRepository));
  }

  /// Dispose the service
  void dispose() {
    Logger.debug('InAppRatingService: Service disposed');
  }
}