import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/repositories/ideabook_repository.dart';
import 'package:noeji/repositories/idea_repository.dart';
import 'package:noeji/repositories/note_repository.dart';
import 'package:noeji/repositories/repository_providers.dart';
import 'package:noeji/utils/logger.dart';

/// Service for exporting ideabooks to various formats
class ExportService {
  const ExportService({
    required this.ideabookRepository,
    required this.ideaRepository,
    required this.noteRepository,
  });

  final IdeabookRepository ideabookRepository;
  final IdeaRepository ideaRepository;
  final NoteRepository noteRepository;

  /// Export all ideabooks as markdown
  Future<String> exportAsMarkdown() async {
    try {
      Logger.debug('Starting markdown export');
      
      // Get all ideabooks
      final ideabooks = await ideabookRepository.getAllIdeabooks();
      Logger.debug('Found ${ideabooks.length} ideabooks');

      final buffer = StringBuffer();
      
      for (final ideabook in ideabooks) {
        // Add ideabook header
        buffer.writeln('# ${_sanitizeForMarkdown(ideabook.name)}');
        buffer.writeln();
        
        // Get ideas for this ideabook
        final ideas = await ideaRepository.getIdeasByIdeabookId(ideabook.id);
        
        // Get notes for this ideabook
        final notes = await noteRepository.getNotesByIdeabookId(ideabook.id);
        
        // Sort ideas by sortOrder if available, otherwise by creation date
        ideas.sort((a, b) {
          if (a.sortOrder != null && b.sortOrder != null) {
            return a.sortOrder!.compareTo(b.sortOrder!);
          }
          return a.createdAt.compareTo(b.createdAt);
        });
        
        // Sort notes by sortOrder if available, otherwise by creation date
        notes.sort((a, b) {
          if (a.sortOrder != null && b.sortOrder != null) {
            return a.sortOrder!.compareTo(b.sortOrder!);
          }
          return a.createdAt.compareTo(b.createdAt);
        });
        
        // Add ideas section
        if (ideas.isNotEmpty) {
          buffer.writeln('## Ideas');
          buffer.writeln();
          
          for (int i = 0; i < ideas.length; i++) {
            final idea = ideas[i];
            buffer.writeln(_sanitizeForMarkdown(idea.content));
            
            // Add separator between ideas (except for the last one)
            if (i < ideas.length - 1) {
              buffer.writeln();
              buffer.writeln('---');
              buffer.writeln();
            }
          }
          buffer.writeln();
        }
        
        // Add notes section
        if (notes.isNotEmpty) {
          buffer.writeln('## Notes');
          buffer.writeln();
          
          for (int i = 0; i < notes.length; i++) {
            final note = notes[i];
            buffer.writeln('**${_sanitizeForMarkdown(note.title)}**: ${_sanitizeForMarkdown(note.content)}');
            
            // Add separator between notes (except for the last one)
            if (i < notes.length - 1) {
              buffer.writeln();
              buffer.writeln('---');
              buffer.writeln();
            }
          }
          buffer.writeln();
        }
        
        // Add separator between ideabooks (except for the last one)
        if (ideabook != ideabooks.last) {
          buffer.writeln('---');
          buffer.writeln();
        }
      }
      
      Logger.debug('Markdown export completed successfully');
      return buffer.toString();
    } catch (e) {
      Logger.error('Failed to export as markdown: $e');
      rethrow;
    }
  }

  /// Export all ideabooks as HTML
  Future<String> exportAsHtml() async {
    try {
      Logger.debug('Starting HTML export');
      
      // Get all ideabooks
      final ideabooks = await ideabookRepository.getAllIdeabooks();
      Logger.debug('Found ${ideabooks.length} ideabooks');

      final buffer = StringBuffer();
      
      // Add HTML header
      buffer.writeln('<!DOCTYPE html>');
      buffer.writeln('<html>');
      buffer.writeln('<head>');
      buffer.writeln('<meta charset="UTF-8">');
      buffer.writeln('<title>Noeji Ideabooks Export</title>');
      buffer.writeln('<style>');
      buffer.writeln('body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; margin: 20px; line-height: 1.6; }');
      buffer.writeln('h1 { color: #333; border-bottom: 2px solid #333; padding-bottom: 10px; }');
      buffer.writeln('h2 { color: #666; border-bottom: 1px solid #666; margin-top: 30px; margin-bottom: 15px; }');
      buffer.writeln('.ideabook { margin-bottom: 40px; }');
      buffer.writeln('.ideabook:not(:last-child) { border-bottom: 1px solid #eee; padding-bottom: 30px; }');
      buffer.writeln('.idea { margin-bottom: 20px; padding: 10px 0; }');
      buffer.writeln('.note { margin-bottom: 20px; padding: 10px 0; }');
      buffer.writeln('hr { border: none; border-top: 1px solid #ddd; margin: 15px 0; }');
      buffer.writeln('.idea-content { margin: 0; }');
      buffer.writeln('.note-content { margin: 0; }');
      buffer.writeln('</style>');
      buffer.writeln('</head>');
      buffer.writeln('<body>');
      
      for (final ideabook in ideabooks) {
        // Add ideabook div
        buffer.writeln('<div class="ideabook">');
        buffer.writeln('<h1>${_sanitizeForHtml(ideabook.name)}</h1>');
        
        // Get ideas for this ideabook
        final ideas = await ideaRepository.getIdeasByIdeabookId(ideabook.id);
        
        // Get notes for this ideabook
        final notes = await noteRepository.getNotesByIdeabookId(ideabook.id);
        
        // Sort ideas by sortOrder if available, otherwise by creation date
        ideas.sort((a, b) {
          if (a.sortOrder != null && b.sortOrder != null) {
            return a.sortOrder!.compareTo(b.sortOrder!);
          }
          return a.createdAt.compareTo(b.createdAt);
        });
        
        // Sort notes by sortOrder if available, otherwise by creation date
        notes.sort((a, b) {
          if (a.sortOrder != null && b.sortOrder != null) {
            return a.sortOrder!.compareTo(b.sortOrder!);
          }
          return a.createdAt.compareTo(b.createdAt);
        });
        
        // Add ideas section
        if (ideas.isNotEmpty) {
          buffer.writeln('<h2>Ideas</h2>');
          
          for (int i = 0; i < ideas.length; i++) {
            final idea = ideas[i];
            buffer.writeln('<div class="idea">');
            buffer.writeln('<p class="idea-content">${_sanitizeForHtml(idea.content)}</p>');
            buffer.writeln('</div>');
            
            // Add separator between ideas (except for the last one)
            if (i < ideas.length - 1) {
              buffer.writeln('<hr>');
            }
          }
        }
        
        // Add notes section
        if (notes.isNotEmpty) {
          buffer.writeln('<h2>Notes</h2>');
          
          for (int i = 0; i < notes.length; i++) {
            final note = notes[i];
            buffer.writeln('<div class="note">');
            buffer.writeln('<p class="note-content"><strong>${_sanitizeForHtml(note.title)}</strong>: ${_sanitizeForHtml(note.content)}</p>');
            buffer.writeln('</div>');
            
            // Add separator between notes (except for the last one)
            if (i < notes.length - 1) {
              buffer.writeln('<hr>');
            }
          }
        }
        
        buffer.writeln('</div>');
      }
      
      // Close HTML
      buffer.writeln('</body>');
      buffer.writeln('</html>');
      
      Logger.debug('HTML export completed successfully');
      return buffer.toString();
    } catch (e) {
      Logger.error('Failed to export as HTML: $e');
      rethrow;
    }
  }


  /// Export ideas from a specific ideabook as markdown
  Future<String> exportIdeasAsMarkdown(String ideabookId) async {
    try {
      Logger.debug('Starting ideas markdown export for ideabook: $ideabookId');
      
      // Get ideas for this ideabook
      final ideas = await ideaRepository.getIdeasByIdeabookId(ideabookId);
      Logger.debug('Found ${ideas.length} ideas');

      final buffer = StringBuffer();
      
      // Sort ideas by sortOrder if available, otherwise by creation date
      ideas.sort((a, b) {
        if (a.sortOrder != null && b.sortOrder != null) {
          return a.sortOrder!.compareTo(b.sortOrder!);
        }
        return a.createdAt.compareTo(b.createdAt);
      });
      
      for (int i = 0; i < ideas.length; i++) {
        final idea = ideas[i];
        
        // Add idea content directly (each idea is separate to prevent formatting issues)
        buffer.writeln(_sanitizeForMarkdown(idea.content));
        
        // Add separator between ideas (except for the last one)
        if (i < ideas.length - 1) {
          buffer.writeln();
          buffer.writeln('---');
          buffer.writeln();
        }
      }
      
      Logger.debug('Ideas markdown export completed successfully');
      return buffer.toString();
    } catch (e) {
      Logger.error('Failed to export ideas as markdown: $e');
      rethrow;
    }
  }

  /// Export ideas from a specific ideabook as HTML
  Future<String> exportIdeasAsHtml(String ideabookId) async {
    try {
      Logger.debug('Starting ideas HTML export for ideabook: $ideabookId');
      
      // Get ideas for this ideabook
      final ideas = await ideaRepository.getIdeasByIdeabookId(ideabookId);
      Logger.debug('Found ${ideas.length} ideas');

      final buffer = StringBuffer();
      
      // Add HTML header
      buffer.writeln('<!DOCTYPE html>');
      buffer.writeln('<html>');
      buffer.writeln('<head>');
      buffer.writeln('<meta charset="UTF-8">');
      buffer.writeln('<title>Ideas Export</title>');
      buffer.writeln('<style>');
      buffer.writeln('body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; margin: 20px; line-height: 1.6; }');
      buffer.writeln('h2 { color: #333; border-bottom: 1px solid #333; margin-top: 30px; margin-bottom: 15px; }');
      buffer.writeln('.idea { margin-bottom: 20px; padding: 10px 0; }');
      buffer.writeln('hr { border: none; border-top: 1px solid #ddd; margin: 15px 0; }');
      buffer.writeln('.idea-content { margin: 0; }');
      buffer.writeln('</style>');
      buffer.writeln('</head>');
      buffer.writeln('<body>');
      
      // Sort ideas by sortOrder if available, otherwise by creation date
      ideas.sort((a, b) {
        if (a.sortOrder != null && b.sortOrder != null) {
          return a.sortOrder!.compareTo(b.sortOrder!);
        }
        return a.createdAt.compareTo(b.createdAt);
      });
      
      for (int i = 0; i < ideas.length; i++) {
        final idea = ideas[i];
        
        // Add idea div without numbered title
        buffer.writeln('<div class="idea">');
        buffer.writeln('<p class="idea-content">${_sanitizeForHtml(idea.content)}</p>');
        buffer.writeln('</div>');
        
        // Add separator between ideas (except for the last one)
        if (i < ideas.length - 1) {
          buffer.writeln('<hr>');
        }
      }
      
      // Close HTML
      buffer.writeln('</body>');
      buffer.writeln('</html>');
      
      Logger.debug('Ideas HTML export completed successfully');
      return buffer.toString();
    } catch (e) {
      Logger.error('Failed to export ideas as HTML: $e');
      rethrow;
    }
  }


  /// Fix markdown syntax issues to prevent formatting errors
  String _fixMarkdownSyntax(String text) {
    // Count and fix mismatched asterisks
    final asteriskCount = text.split('*').length - 1;
    String fixedText = text;
    
    // If odd number of asterisks, escape the last one
    if (asteriskCount % 2 != 0) {
      final lastAsteriskIndex = fixedText.lastIndexOf('*');
      if (lastAsteriskIndex != -1) {
        fixedText = '${fixedText.substring(0, lastAsteriskIndex)}\\*${fixedText.substring(lastAsteriskIndex + 1)}';
      }
    }
    
    // Fix other common markdown syntax issues
    fixedText = fixedText
        .replaceAll('**', '\\*\\*') // Escape double asterisks if they appear isolated
        .replaceAll(RegExp(r'(?<!\*)\*(?!\*)(?![^*]*\*(?!\*))'), '\\*') // Escape single asterisks that don't have pairs
        .replaceAll('\\*\\*', '**'); // Restore intentional double asterisks
    
    return fixedText;
  }

  /// Sanitize text for markdown export (fix syntax issues)
  String _sanitizeForMarkdown(String text) {
    return _fixMarkdownSyntax(text);
  }

  /// Sanitize text for HTML export (escape HTML and fix markdown)
  String _sanitizeForHtml(String text) {
    // First fix markdown syntax, then escape HTML
    final fixedMarkdown = _fixMarkdownSyntax(text);
    return _escapeHtml(fixedMarkdown);
  }

  /// Escape HTML special characters
  String _escapeHtml(String text) {
    return text
        .replaceAll('&', '&amp;')
        .replaceAll('<', '&lt;')
        .replaceAll('>', '&gt;')
        .replaceAll('"', '&quot;')
        .replaceAll("'", '&#x27;');
  }
}

/// Provider for the export service
final exportServiceProvider = Provider<ExportService>((ref) {
  final ideabookRepository = ref.watch(ideabookRepositoryProvider);
  final ideaRepository = ref.watch(ideaRepositoryProvider);
  final noteRepository = ref.watch(noteRepositoryProvider);
  
  return ExportService(
    ideabookRepository: ideabookRepository,
    ideaRepository: ideaRepository,
    noteRepository: noteRepository,
  );
});